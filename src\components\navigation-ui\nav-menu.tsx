"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/shadcn-ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/shadcn-ui/sidebar";
import { INavMenu } from "@/lib/data";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Badge } from "../shadcn-ui/badge";

export function NavMenu({ data }: { data: INavMenu[] }) {
  const pathname = usePathname();
  const { isMobile, setOpenMobile } = useSidebar();

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Menu</SidebarGroupLabel>
      <SidebarMenu>
        {data
          .filter((item) => item.isActive)
          .map((item) => {
            const focusMenu = pathname === item.href;
            const hasActiveSubMenu = item.sub_menu?.some(
              (sub) => pathname === sub.href
            );

            if (!item.sub_menu || item.sub_menu.length === 0) {
              return (
                <SidebarMenuItem key={item.name}>
                  <Link href={item.href}>
                    <SidebarMenuButton
                      className={cn(
                        focusMenu &&
                          "bg-primary hover:bg-primary/80 text-white hover:text-white"
                      )}
                      tooltip={item.name}
                      onClick={() => isMobile && setOpenMobile(false)}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.name}</span>
                      {item.tag && (
                        <Badge
                          className={
                            (cn("rounded-sm uppercase"),
                            focusMenu
                              ? "rounded-sm uppercase bg-primary-foreground hover:bg-primary-foreground/80 text-primary"
                              : "rounded-sm uppercase")
                          }
                        >
                          {item.tag}
                        </Badge>
                      )}
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>
              );
            }

            return (
              <Collapsible
                key={item.name}
                asChild
                defaultOpen={focusMenu || hasActiveSubMenu}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton
                      className={cn(
                        focusMenu && "bg-primary hover:bg-primary/80"
                      )}
                      tooltip={item.name}
                    >
                      {item.icon && <item.icon />}
                      <span>{item.name}</span>
                      {item.tag && (
                        <Badge className="rounded-sm uppercase">
                          {item.tag}
                        </Badge>
                      )}
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.sub_menu
                        .filter((sub) => sub.isActive)
                        .map((sub) => {
                          const focusSubMenu = pathname === sub.href;

                          return (
                            <SidebarMenuSubItem key={sub.name}>
                              <Link href={sub.href}>
                                <SidebarMenuSubButton
                                  className={cn(
                                    focusSubMenu &&
                                      "bg-primary hover:bg-primary/80 text-white hover:text-white"
                                  )}
                                  onClick={() => isMobile && setOpenMobile(false)}
                                  asChild
                                >
                                  <span>{sub.name}</span>
                                </SidebarMenuSubButton>
                              </Link>
                            </SidebarMenuSubItem>
                          );
                        })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            );
          })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
