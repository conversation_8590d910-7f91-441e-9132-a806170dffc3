import CheckStatus from "@/components/page-ui/mqtt/check-status-ui/check-status";
import Monitor from "@/components/page-ui/mqtt/monitor-ui/monitor";
import Playground from "@/components/page-ui/mqtt/playground-ui/playground";
import TitleHeader from "@/components/ui/title-header";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "MQTT - Check Status",
};

export default function MqttCheckStatusPage() {
  return (
    <>
      {/* <TitleHeader title="MQTT - Check Status" type="static" /> */}
      <CheckStatus />
    </>
  );
}
