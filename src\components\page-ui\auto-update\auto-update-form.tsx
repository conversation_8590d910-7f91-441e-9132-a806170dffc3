"use client";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { endPontList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  autoUpdateSchema,
  clearCacheMerchantSchema,
  IAutoUpdateSchema,
  IClearCacheMerchantSchema,
  RegistrationInfo,
} from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useEffect, useState } from "react";
import Combobox from "@/components/ui/combobox";
import { <PERSON>Check<PERSON>ig, CircleX, Loader2, Search } from "lucide-react";
import { Session } from "next-auth";
import AlertModal from "@/components/ui/alert-modal";
import axios from "axios";
import { cn, convertOADateToDateString, endPointToken } from "@/lib/utils";
import { admigLog, getData } from "@/lib/actions";
import CardActionStatus from "@/components/ui/card-action-status";
import { Input } from "@/components/shadcn-ui/input";
import useMqtt from "@/components/hooks/use-mqtt";
import { Decrypt, Encrypt } from "@/lib/encode";
import { useIsMobile } from "@/components/hooks/use-mobile";

type Props = {
  session: Session;
  configs: any;
  folders: string[] | undefined;
};

const mqttHostType = [
  {
    name: "Develop",
    value: "develop",
  },
  {
    name: "Production",
    value: "production",
  },
];

export default function AutoUpdateForm({ session, configs, folders }: Props) {
  const {
    control,
    watch,
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<IAutoUpdateSchema>({
    resolver: zodResolver(autoUpdateSchema),
  });

  const {
    isConnected,
    connectStatus,
    payload,
    isSubscribed,
    subscriptionDetail,
    mqttConnect,
    mqttDisconnect,
    mqttPublish,
    mqttSubscribe,
    mqttUnsubscribe,
  } = useMqtt(getValues("mqttHostType") as string);

  const isMobile = useIsMobile();

  const [loading, setLoading] = useState(false);
  const [loadingCheckButton, setLoadingCheckButton] = useState(false);
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState<boolean | null>(null);
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const [registrationDetail, setRegistrationDetail] =
    useState<RegistrationInfo | null>(null);
  const [regisCodeMessage, setRegisCodeMessage] = useState<string | null>(null);
  const clearAlert = () => setShowAlert(null);

  const validateFormData = (data: IAutoUpdateSchema): boolean => {
    let isValid = true;

    if (!data.version) {
      setError("version", {
        type: "server",
        message: "Please select Version.",
      });
      isValid = false;
    }

    if (!data.customer) {
      setError("customer", {
        type: "server",
        message: "Please select Customer",
      });
      isValid = false;
    }

    return isValid;
  };

  const openAlert = (data: IAutoUpdateSchema) => {
    if (!validateFormData(data)) {
      return;
    }
    setIsAlertOpen(true);
  };

  const handleCloseAlert = () => {
    setIsAlertOpen(false);
  };

  const handleCheckRegistrationCode = async () => {
    setLoadingCheckButton(true);
    const registrationCode = getValues("customer");
    if (!registrationCode) {
      return;
    }

    try {
      const response = await getData(
        'https://infoeasy.cc:7702', // fix ไว้ที่ 113 ,
        `users/customer-registration/${registrationCode}`
      );

      if (!response.data) {
        setRegisCodeMessage("Registration Code Not Found.");
        setLoadingCheckButton(false);
        return;
      }

      setLoadingCheckButton(false);
      setRegisCodeMessage(null);
      setRegistrationDetail({
        registrationId: response.data.registrationId,
        dealerCode: response.data.dealerCode,
        registrationCode: Decrypt(response.data.registrationCode),
        companyName: response.data.companyName,
        expireDate: convertOADateToDateString(
          Number(Decrypt(response.data.expireDate))
        ),
        isPaid: response.data.isPaid,
        useMA: response.data.useMA,
      });
    } catch (error) {
      setLoadingCheckButton(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  const handleConfirm = async () => {
    setLoading(true);
    setIsSuccess(null);

    try {
      const isConnectMQTT = await mqttConnect();

      if (isConnectMQTT) {
        const message = {
          version: getValues("version"),
          ftpPath: process.env.FTP_FOLDER_PATH,
          ftpHost: process.env.FTP_HOST,
          ftpUsername: process.env.FTP_USERNAME,
          ftpPassword: process.env.FTP_PASSWORD,
        };

        const isPublicSuccess = await mqttPublish(
          `${getValues("customer")}/autoupdate`,
          Encrypt(JSON.stringify(message))
        );
        if (isPublicSuccess) {
          setLoading(false);
          setIsSuccess(true);
          mqttDisconnect();
        } else {
          setLoading(false);
          setIsSuccess(false);
          setShowAlert({
            type: "warning",
            detail: "Publish message failed, please try again later",
            onClose: clearAlert,
          });
        }
      } else {
        setLoading(false);
        setIsSuccess(false);
        setShowAlert({
          type: "warning",
          detail: "Mqtt connection failed, please try again later",
          onClose: clearAlert,
        });
      }
    } catch (error) {
      setIsSuccess(false);
      setLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="flex flex-col gap-4 justify-center items-center w-full relative">
        <div className="w-full lg:w-[calc(100%-250px)] xl:w-[calc(100%-500px)] transition-all">
          <form onSubmit={handleSubmit(openAlert)}>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl md:text-2xl">
                  Auto Update POS
                </CardTitle>
                <CardDescription>
                  {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6">
                  <div className="grid gap-3">
                    <Label htmlFor="mqttHostType">MQTT Host</Label>
                    <Combobox
                      title="MQTT Host"
                      listData={mqttHostType}
                      nameKey="name"
                      valueKey="value"
                      showResetButton={true}
                      onValueChange={(selectedValue) => {
                        setValue("mqttHostType", selectedValue),
                          setIsSuccess(null);
                      }}
                    />
                    {errors.mqttHostType && (
                      <p className="text-sm text-red-500">
                        {errors.mqttHostType.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="project">Version</Label>
                    <Combobox
                      title="Version"
                      listData={folders}
                      nameKey="name"
                      valueKey="name"
                      showResetButton={true}
                      onValueChange={(selectedValue) => {
                        setValue("version", selectedValue), setIsSuccess(null);
                      }}
                    />
                    {errors.version && (
                      <p className="text-sm text-red-500">
                        {errors.version.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="merchantId">
                      Customer Registration Code
                    </Label>
                    {/* <Combobox
                      title="Registration"
                      listData={registration}
                      valueKey="registrationCode"
                      nameKey="companyName"
                      showValueWithName={false}
                      onValueChange={(selectedValue) => {
                        setValue("customer", selectedValue), setIsClear(null);
                      }}
                    /> */}
                    <div className="flex flex-row items-center gap-2">
                      <Input
                        {...register("customer")}
                        id="customer"
                        name="customer"
                        type="text"
                        placeholder="Enter Registration Code"
                        onChange={(e) => {
                          setValue("customer", e.target.value),
                            setIsSuccess(null);
                        }}
                      />
                      <Button
                        className="bg-amber-500/80 hover:bg-amber-600/80"
                        type="button"
                        variant={"default"}
                        size={'sm'}
                        onClick={handleCheckRegistrationCode}
                        disabled={!watch("customer")}
                      >
                        <span className="flex items-center justify-center gap-2">
                          {loadingCheckButton ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <>
                              {isMobile ? (
                                <Search className="h-4 w-4" />
                              ) : (
                                "Check"
                              )}
                            </>
                          )}
                        </span>
                      </Button>
                    </div>
                    {errors.customer && (
                      <p className="text-sm text-red-500">
                        {errors.customer.message}
                      </p>
                    )}
                    {regisCodeMessage && (
                      <p className="text-sm text-red-500">{regisCodeMessage}</p>
                    )}
                  </div>
                  {registrationDetail && (
                    <div className="bg-zinc-800 p-2 rounded-md">
                      <table className="table-auto border rounded-md w-full text-sm">
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">Registration ID</td>
                          <td className="p-1">
                            {registrationDetail.registrationId}
                          </td>
                        </tr>
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">Dealer Code</td>
                          <td className="p-1">
                            {registrationDetail.dealerCode}
                          </td>
                        </tr>
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">Registration Code</td>
                          <td className="p-1">
                            {registrationDetail.registrationCode}
                          </td>
                        </tr>
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">Company Name</td>
                          <td className="p-1">
                            {registrationDetail.companyName}
                          </td>
                        </tr>
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">Expired Date</td>
                          <td className="p-1">
                            {registrationDetail.expireDate}
                          </td>
                        </tr>
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">IsPaid</td>
                          <td className="p-1">
                            {registrationDetail.isPaid ? "true" : "false"}
                          </td>
                        </tr>
                        <tr className="border rounded-md border-zinc-500/50">
                          <td className="p-1 px-2">UsePaid</td>
                          <td className="p-1">
                            {registrationDetail.useMA ? "true" : "false"}
                          </td>
                        </tr>
                      </table>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="gap-2 justify-center">
                <Button
                  type="submit"
                  disabled={isSubmitting || loading || !registrationDetail}
                  className="w-full"
                >
                  <span className="flex items-center justify-center gap-2">
                    {loading ? (
                      <>
                        <p>Updating...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Update</p>
                      </>
                    )}
                  </span>
                </Button>
              </CardFooter>
            </Card>
            <AlertModal
              title="Update Confirmation"
              desc="This action will update the data on our servers. This process cannot be undone."
              type="static"
              open={isAlertOpen}
              isVerify={false}
              onClose={handleCloseAlert}
              onSubmit={handleConfirm}
            />
          </form>
        </div>
        <CardActionStatus
          loading={loading}
          isSuccess={isSuccess}
          successMessage="Updated Success"
          errorMessage="Updated failed"
          loadingMessage={
            connectStatus === "Connecting" ? connectStatus : `Updating...`
          }
        />
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
