import ClearCacheAll from "@/components/page-ui/clear-cache/clear-cache-all";
import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { getServerSession, Session } from "next-auth";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Clear Cache All",
};

export default async function ClearCacheMerchantPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();

  return (
    <>
      {/* <TitleHeader title="Clear Cache" type="static" /> */}
      <ClearCacheAll configs={configs} session={session as Session}/>
    </>
  );
}
