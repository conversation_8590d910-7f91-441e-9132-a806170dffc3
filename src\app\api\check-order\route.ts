import { authOptions } from "@/auth";
import { endPointToken } from "@/lib/utils";
import axios from "axios";
import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }
  
  const endPoint = req.nextUrl.searchParams.get("endPoint");
  const checkUrl = req.nextUrl.searchParams.get("checkUrl");

  try {
    const response = await axios.get(`${endPoint}/api/checkorder?${checkUrl}`, {
      headers: {
        Accept: "application/json",
        key: endPointToken(endPoint as string),
      },
    });
    return NextResponse.json(response.data, { status: 200 });
  } catch (error) {
    console.error("Error fetching data:", error);
    return NextResponse.json(
      { error: "Failed to fetch data" },
      { status: 500 }
    );
  }
}
