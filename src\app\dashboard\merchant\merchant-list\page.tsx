import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from 'react';
import MerchantList from "@/components/page-ui/merchant/merchant-list";

export const metadata: Metadata = {
    title: "Merchant List",
};

export default async function MerchantListPage() {
    const session = await getServerSession(authOptions);
    const configs = await getConfig();
    if (!session) {
        return <div>Please log in to access this page.</div>;
    }
    return (
        <>
            { <MerchantList session={session as Session} configs={configs} /> }
        </>
    )
}