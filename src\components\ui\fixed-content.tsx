import { cn } from "@/lib/utils";
import React, { useEffect, useRef, useState } from "react";

type Props = {
  children: React.ReactNode;
  parentClassName: string;
  childClassName: string;
};

export default function FixedContent({
  children,
  parentClassName,
  childClassName,
}: Props) {
  const parentRef = useRef<HTMLDivElement>(null);
  const fixedRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<string | undefined>("auto");

  const updateWidth = () => {
    if (parentRef.current) {
      setWidth(parentRef.current.offsetWidth + "px");
    } else {
      setWidth(undefined); // Default to "auto"
    }
  };

  useEffect(() => {
    const observer = new ResizeObserver(() => {
      updateWidth();
    });

    if (parentRef.current) {
      observer.observe(parentRef.current);
    }

    return () => {
      if (parentRef.current) {
        observer.unobserve(parentRef.current);
      }
    };
  }, []);

  useEffect(() => {
    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  return (
    <div className={cn(parentClassName)} ref={parentRef}>
      <div className={cn(childClassName)} ref={fixedRef} style={{ width }}>
        {children}
      </div>
    </div>
  );
}
