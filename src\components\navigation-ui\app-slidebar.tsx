"use client"

import * as React from "react"

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON><PERSON>nt,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/shadcn-ui/sidebar"
import { NavTitle } from "./nav-title"
import { NavMenu } from "./nav-menu"
import { NavUser } from "./nav-user"
import { Infogrammer, navMenu } from "@/lib/data"
import { useSession } from "next-auth/react"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  
  return (
    <Sidebar className="z-50" collapsible="icon" {...props}>
      <SidebarHeader>
        <NavTitle data={Infogrammer[0]} />
      </SidebarHeader>
      <SidebarContent>
        <NavMenu data={navMenu} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={session?.user as any} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
