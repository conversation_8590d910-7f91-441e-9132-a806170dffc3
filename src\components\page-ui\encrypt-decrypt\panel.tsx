"use client";
import { But<PERSON> } from "@/components/shadcn-ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Textarea } from "@/components/shadcn-ui/textarea";
import { Decrypt, Encrypt } from "@/lib/encode";
import React, { useState } from "react";

export default function EncryptDecryptPanel() {
  const [textForEncrypt, setTextForEncrypt] = useState<string>("");
  const [resultEncrypt, setResultEncrypt] = useState<string>("");
  const [textForDecrypt, setTextForDecrypt] = useState<string>("");
  const [resultDecrypt, setResultDecrypt] = useState<string>("");
  const [textUrl, setTextUrl] = useState<string>("");
  const [resultUrl, setResultUrl] = useState<string>("");

  const handleEncrypt = () => {
    const encryptText = Encrypt(textForEncrypt);
    setResultEncrypt(encryptText);
  };

  const handleDecrypt = () => {
    const decryptText = Decrypt(decodeURIComponent(textForDecrypt));
    setResultDecrypt(decryptText);
  };

  const handleUrlDecode = () => {
    const urlDecodeText = decodeURIComponent(textUrl);
    setResultUrl(urlDecodeText);
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      {/* <div className="flex flex-col gap-4 w-full relative">
        <div className="w-full lg:w-[calc(100%-250px)] xl:w-[calc(100%-500px)] transition-all"> */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 w-full">
        {/* ENCRYPT */}
        <Card className="col-span-2 lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-xl md:text-2xl">Encrypt</CardTitle>
            <CardDescription>Encoding for Infogrammer Team.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex flex-row justify-between items-start gap-2">
                <Textarea
                  name="textString"
                  placeholder="Enter Text String"
                  rows={3}
                  value={textForEncrypt}
                  onChange={(e) => setTextForEncrypt(e.target.value)}
                />
                <div className="flex flex-col gap-2">
                  <Button
                    type="button"
                    onClick={handleEncrypt}
                    size={"sm"}
                    variant={"default"}
                  >
                    Encrypt
                  </Button>
                  <Button
                    type="reset"
                    onClick={() => {
                      setTextForEncrypt(""), setResultEncrypt("");
                    }}
                    size={"sm"}
                    variant={"default"}
                    className="bg-transparent border border-primary text-primary hover:bg-primary/20"
                  >
                    Clear
                  </Button>
                </div>
              </div>
              <Textarea
                name="encryptResult"
                placeholder="Encrypt Result"
                value={resultEncrypt}
                rows={1}
                onChange={(e) => setResultEncrypt(e.target.value)}
                disabled={!resultEncrypt}
              />
            </div>
          </CardContent>
        </Card>

        {/* DECRYPT */}
        <Card className="col-span-2 lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-xl md:text-2xl">Decrypt + URL Decode</CardTitle>
            <CardDescription>Decoding for Infogrammer Team.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex flex-row justify-between items-start gap-2">
                <Textarea
                  name="textEncrypt"
                  placeholder="Enter Encrypt Text"
                  rows={3}
                  value={textForDecrypt}
                  onChange={(e) => setTextForDecrypt(e.target.value)}
                />
                <div className="flex flex-col gap-2">
                  <Button
                    type="button"
                    onClick={handleDecrypt}
                    size={"sm"}
                    variant={"default"}
                  >
                    Decrypt
                  </Button>
                  <Button
                    type="reset"
                    onClick={() => {
                      setTextForDecrypt(""), setResultDecrypt("");
                    }}
                    size={"sm"}
                    variant={"default"}
                    className="bg-transparent border border-primary text-primary hover:bg-primary/20"
                  >
                    Clear
                  </Button>
                </div>
              </div>
              <Textarea
                name="decryptResult"
                placeholder="Decrypt Result"
                value={resultDecrypt}
                rows={1}
                onChange={(e) => setResultDecrypt(e.target.value)}
                disabled={!resultDecrypt}
              />
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-2">
          <CardHeader>
            <CardTitle className="text-xl md:text-2xl">URL Decode</CardTitle>
            <CardDescription>Url Decode for Infogrammer Team.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex flex-row justify-between items-start gap-2">
                <Textarea
                  name="url"
                  placeholder="Enter URL"
                  rows={5}
                  value={textUrl}
                  onChange={(e) => setTextUrl(e.target.value)}
                />
                <div className="flex flex-col gap-2">
                  <Button
                    type="button"
                    onClick={handleUrlDecode}
                    size={"sm"}
                    variant={"default"}
                  >
                    Decode
                  </Button>
                  <Button
                    type="reset"
                    onClick={() => {
                      setTextUrl(""), setResultUrl("");
                    }}
                    size={"sm"}
                    variant={"default"}
                    className="bg-transparent border border-primary text-primary hover:bg-primary/20"
                  >
                    Clear
                  </Button>
                </div>
              </div>
              <Textarea
                name="decryptResult"
                placeholder="Url Decode Result"
                value={resultUrl}
                rows={5}
                onChange={(e) => setResultUrl(e.target.value)}
                disabled={!resultUrl}
              />
            </div>
          </CardContent>
        </Card>
      </div>
      {/* </div>
      </div> */}
    </div>
  );
}
