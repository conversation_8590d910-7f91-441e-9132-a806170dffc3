"use client";
import {
    <PERSON>,
    CardContent,
    CardDescription,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON>itle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/shadcn-ui/select";
import { endPontList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ISearchMerchantIdSchema, searchMerchantIdSchema } from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useEffect, useState, useCallback } from "react";
import Combobox from "@/components/ui/combobox";
import FixedContent from "@/components/ui/fixed-content";
import { Session } from "next-auth";
import axios from "axios";
import { Loader2 } from "lucide-react";
import { endPointToken } from "@/lib/utils";
import { admigLog } from "@/lib/actions";
import { Input } from "@/components/shadcn-ui/input";

import MerchantDetailFrom from "@/components/page-ui/merchant/merchant-detail-from";
import MerchantConfigTable from "@/components/table/merchant-config-table";



type Props = {
    session: Session;
    configs: any;
};

export default function SearchMerchantID({ session, configs }: Props) {
    const isFixProject = configs.isFixProject;

    const {
        control,
        register,
        getValues,
        setValue,
        reset,
        handleSubmit,
        formState: { errors, isSubmitting },
        setError,
    } = useForm<ISearchMerchantIdSchema>({
        resolver: zodResolver(searchMerchantIdSchema),
        defaultValues: {
            endPoint: isFixProject ? session.user.endPoint : "",
            merchantId: "",
        },
    });
    const [data, setData] = useState<any>();
    const [merchant, setMerchant] = useState<any>(null);
    const [merchantLoading, setMerchantLoading] = useState(false);
    const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
    const [isSelectedKT, setIsSelectedKT] = useState(false);

    const clearAlert = () => setShowAlert(null);

    //** ✅ ดึงข้อมูล Merchant จาก API */
    const findMerchant = useCallback(async (endPoint: string, apiPath: string) => {
        setMerchantLoading(true);
        setMerchant(null);
        try {
            const response = await axios.get(`${endPoint}/api/${apiPath}`, {
                headers: {
                    Accept: "application/json",
                    key: endPointToken(endPoint),
                },
            });
            if (response.status === 200) {
                setMerchant(response.data);
            } else {
                setShowAlert({
                    type: "warning",
                    detail: "ค้นหาไม่สำเร็จ กรุณาลองใหม่อีกครั้ง",
                    onClose: clearAlert,
                });
            }
        } catch {
            setShowAlert({
                type: "error",
                detail: "เกิดข้อผิดพลาด ไม่สามารถค้นหา Merchant ID ได้",
                onClose: clearAlert,
            });
        } finally {
            setMerchantLoading(false);
        }
    }, [clearAlert]);

    //** ✅ เลือก Project แล้วโหลด Merchant */
    const handleProjectChange = useCallback(
        (value: string, onChange?: (val: string) => void) => {
            if (onChange) onChange(value);
            setValue("endPoint", value);
            setMerchant(null);
            const selectedEndPoint = endPontList.find((endpoint) => endpoint.url === value);
            if (selectedEndPoint?.name === "API_KT") {
                setIsSelectedKT(true);
            } else {
                setIsSelectedKT(false);
                findMerchant(value, "merchant/filter");
            }
        },
        [findMerchant, setValue]
    );

    //** ✅ Render Dropdown เลือก Project */
    const renderSelectProject = () => {
        const renderSelectItems = () =>
            endPontList.map((item) => (
                <SelectItem key={item.id} value={item.url}>
                    {item.name}
                </SelectItem>
            ));

        return isFixProject ? (
            <Controller
                name="endPoint"
                control={control}
                render={({ field }) => {
                    const selectedItem = endPontList.find((item) => item.url === field.value);
                    return (
                        <Select
                            onValueChange={(value) => handleProjectChange(value, field.onChange)}
                            value={field.value}
                            disabled={isFixProject}
                        >
                            <SelectTrigger id="project">
                                <SelectValue placeholder="เลือกโปรเจกต์">
                                    {selectedItem ? selectedItem.name : "เลือกโปรเจกต์"}
                                </SelectValue>
                            </SelectTrigger>
                            <SelectContent>{renderSelectItems()}</SelectContent>
                        </Select>
                    );
                }}
            />
        ) : (
            <Select onValueChange={(value) => handleProjectChange(value)}>
                <SelectTrigger id="project">
                    <SelectValue placeholder="เลือกโปรเจกต์" />
                </SelectTrigger>
                <SelectContent>{renderSelectItems()}</SelectContent>
            </Select>
        );
    };

    //** ✅ Event Validate From Filter Merchant */
    const validateFormData = (data: ISearchMerchantIdSchema): boolean => {
        let isValid = true;
        if (!data.endPoint) {
            setError("endPoint", {
                type: "server",
                message: "Please select Project.",
            });
            isValid = false;
        }
        if (!data.merchantId) {
            setError("merchantId", {
                type: "server",
                message: "Please select Merchant ID.",
            });
            isValid = false;
        }

        return isValid;
    }

    //** ✅ Submit ฟอร์ม */
    const onSubmit = async (data: ISearchMerchantIdSchema) => {
        if (!validateFormData(data)) {
            return;
        }
        const { endPoint, merchantId } = data;
        let url = `merchant/${merchantId}`;
        try {

            //** Method Call API ดึงข้อมูล Merchat Detail */
            const response = await axios.get(`${endPoint}/api/${url}`, {
                headers: {
                    Accept: "application/json",
                    key: endPointToken(endPoint),
                },
            });

            //** Method Check Response Status ส่งข้อมูลค้นหา Merchant */
            if (response.status === 200) {
                setData(response.data);
            } else {
                setShowAlert({
                    type: "warning",
                    detail: "Search failed, please try again.",
                    onClose: clearAlert,
                });
                return;
            }
        } catch {
            setShowAlert({
                type: "error",
                detail: "Something went wrong, please try again later",
                onClose: clearAlert,
            });
        }
    };

    //** ✅ เคลียร์ฟอร์ม */
    const handleClearForm = () => {
        reset({
            endPoint: isFixProject ? session.user.endPoint : "",
            merchantId: "",
        });
        setMerchant(null);
        setIsSelectedKT(false);
        clearAlert();
    }

    return (
        <div className="flex flex-1 items-start justify-start">

            <div className="grid gap-4 grid-cols-12 w-full relative">
                <FixedContent parentClassName="col-span-12 lg:col-span-6 xl:col-span-5" childClassName="lg:fixed lg:z-10 lg:w-full">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-xl md:text-2xl">
                                    ค้นหารายละเอียด Merchant
                                </CardTitle>
                                <CardDescription>เลือกโปรเจกต์ และเลือก Merchant ID</CardDescription>
                            </CardHeader>

                            <CardContent>
                                <div className="grid gap-6">
                                    {/* ✅ Project Select */}
                                    <div className="grid gap-3">
                                        <Label htmlFor="project">โปรเจกต์</Label>
                                        {renderSelectProject()}
                                        {errors.endPoint && (
                                            <p className="text-sm text-red-500">{errors.endPoint.message}</p>
                                        )}
                                    </div>

                                    {/* ✅ Merchant Input */}
                                    <div className="grid gap-3">
                                        <Label htmlFor="merchantId">Merchant ID</Label>
                                        {isSelectedKT ? (
                                            <Input
                                                {...register("merchantId")}
                                                id="merchantId"
                                                type="text"
                                                placeholder="กรอก Merchant ID"
                                            />
                                        ) : (
                                            <Combobox
                                                title="Merchant ID"
                                                listData={merchant}
                                                valueKey="merchantId"
                                                nameKey="merchantName"
                                                slugKey="slug"
                                                loading={merchantLoading}
                                                showValueWithName={true}
                                                onValueChange={(selectedValue) => {
                                                    setValue("merchantId", selectedValue);
                                                }}
                                            />
                                        )}
                                        {merchant && errors.merchantId && (
                                            <p className="text-sm text-red-500">{errors.merchantId.message}</p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>

                            {/* ✅ Footer Buttons */}
                            <CardFooter className="gap-2 justify-center">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleClearForm}
                                    className="w-full"
                                >
                                    เคลียร์ฟอร์ม
                                </Button>
                                <Button type="submit" disabled={isSubmitting} className="w-full">
                                    <span className="flex items-center justify-center gap-2">
                                        {isSubmitting ? (
                                            <>
                                                <p>กำลังค้นหา...</p>
                                                <Loader2 className="h-4 w-4 animate-spin" />
                                            </>
                                        ) : (
                                            <p>ค้นหา</p>
                                        )}
                                    </span>
                                </Button>
                            </CardFooter>
                        </Card>
                    </form>
                </FixedContent>

                <div className="col-span-12 lg:col-span-6 xl:col-span-7 space-y-4">
                    <MerchantDetailFrom
                        isLoading={isSubmitting}
                        data={data}
                        endpoint={getValues("endPoint")}
                        onSearch={onSubmit}
                        merchantId={getValues("merchantId")}
                        session={session}
                    />
                    <MerchantConfigTable
                        isLoading={isSubmitting}
                        data={data}
                        endpoint={getValues("endPoint")}
                        onSearch={onSubmit}
                        merchantId={getValues("merchantId")}
                        session={session}
                    />
                </div>
            </div>

            {showAlert && (
                <AlertBar type={showAlert.type} detail={showAlert.detail} onClose={clearAlert} />
            )}
        </div>
    );
}
