"use client";

import * as React from "react";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "../shadcn-ui/sidebar";
import { useState } from "react";
import Image from "next/image";
import { Card } from "../shadcn-ui/card";
import { IInfogrammer } from "@/lib/data";

export function NavTitle({
  data,
}: {
  data: IInfogrammer;
}) {
  const [activeTitle, setActiveTitle] = useState(data);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <Card className="bg-transparent border-none">
          <SidebarMenuButton
            size="lg"
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <Image
                src={data.logo}
                alt="info_icon"
                width={50}
                height={50}
                className="h-8 w-8 rounded-lg"
                loading="lazy"
              />
            </div>
            <div className="grid flex-1 text-left text-base leading-tight">
              <span className="truncate font-semibold uppercase">{activeTitle.name}</span>
              <span className="truncate text-xs">{activeTitle.subName}</span>
            </div>
          </SidebarMenuButton>
        </Card>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
