"use client";
import { useEffect, useRef, useState } from "react";
import mqtt, { MqttClient, IClientOptions } from "mqtt";
import { decodeJSONMessage } from "@/lib/utils";
import {
  initialMQTTConnectionProduction,
  initialMQTTConnectionUAT,
} from "@/lib/data";

interface MessagePayload {
  topic: string;
  message: any;
}

const useMqtt = (
  mqttHostType: "develop" | "production" | string = "develop"
) => {
  const brokerUrl =
    mqttHostType === "develop"
      ? `${initialMQTTConnectionUAT.protocol}://${initialMQTTConnectionUAT.host}:${initialMQTTConnectionUAT.port}`
      : `${initialMQTTConnectionProduction.protocol}://${initialMQTTConnectionProduction.host}:${initialMQTTConnectionProduction.port}`;

  const options: IClientOptions = {
    clientId:
      mqttHostType === "develop"
        ? initialMQTTConnectionUAT.clientId
        : initialMQTTConnectionProduction.clientId,
    username:
      mqttHostType === "develop"
        ? initialMQTTConnectionUAT.username
        : initialMQTTConnectionProduction.username,
    password:
      mqttHostType === "develop"
        ? initialMQTTConnectionUAT.password
        : initialMQTTConnectionProduction.password,
    clean: true,
    reconnectPeriod: 5000, // Reconnect every 5 seconds
    connectTimeout: 30 * 1000, // Timeout in 30 seconds
  };

  const [client, setClient] = useState<MqttClient | null>(null);
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [connectStatus, setConnectStatus] = useState<string>("Connect");
  const [payload, setPayload] = useState<MessagePayload | null>(null);
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);
  const [subscriptionDetail, setSubscriptionDetail] = useState<string | null>(
    null
  );
  const clientRef = useRef<MqttClient | null>(null);

  const mqttConnect = async (): Promise<boolean> => {
    return new Promise((resolve) => {
      try {
        if (clientRef.current?.connected) {
          console.warn("MQTT is already connected");
          resolve(true);
          return;
        }

        setConnectStatus("Connecting");
        const newClient = mqtt.connect(brokerUrl, options);
        clientRef.current = newClient; // เก็บ client ไว้ใน useRef

        newClient.on("connect", () => {
          setIsConnected(true);
          console.info("MQTT Connected");
          setConnectStatus("Connected");
          resolve(true);
        });

        newClient.on("error", (error) => {
          console.error("MQTT Connection error:", error);
          setConnectStatus("Connect");
          setIsConnected(false);
          resolve(false);
        });

        newClient.on("reconnect", () => {
          setConnectStatus("Reconnecting");
          setIsConnected(false);
          resolve(false);
        });
      } catch (error) {
        console.error("MQTT Connection exception:", error);
        resolve(false);
      }
    });
  };

  useEffect(() => {
    if (!clientRef.current) return;

    const client = clientRef.current;

    const handleConnect = () => {
      setConnectStatus("Connected");
      setIsConnected(true);
    };

    const handleError = (err: any) => {
      console.error("MQTT Connection error:", err);
      setIsConnected(false);
    };

    const handleReconnect = () => {
      setConnectStatus("Reconnecting");
    };

    const handleMessage = (topic: string, message: any) => {
      const decodedMessage = decodeJSONMessage(message.toString());
      setPayload({ topic, message: decodedMessage });
    };

    client.on("connect", handleConnect);
    client.on("error", handleError);
    client.on("reconnect", handleReconnect);
    client.on("message", handleMessage);

    return () => {
      client.off("connect", handleConnect);
      client.off("error", handleError);
      client.off("reconnect", handleReconnect);
      client.off("message", handleMessage);

      // ป้องกัน client.end() ปิดการเชื่อมต่อโดยไม่ได้ตั้งใจ
      if (client.connected) {
        console.log("Disconnecting MQTT...");
        client.end();
      }
    };
  }, [clientRef.current]);

  // ✅ ฟังก์ชันตัดการเชื่อมต่อ MQTT
  const mqttDisconnect = (): boolean => {
    if (clientRef.current) {
      try {
        clientRef.current.end(false, () => {
          setConnectStatus("Connect");
          setIsConnected(false);
          setPayload(null);
          console.log("MQTT Disconnected");
        });
        return true;
      } catch (error) {
        console.error("MQTT Disconnect error:", error);
        return false;
      }
    }
    return false;
  };

  // ✅ ฟังก์ชัน Publish ข้อความ
  const mqttPublish = async (
    topic: string,
    message: any,
    qos: 0 | 1 | 2 = 0
  ): Promise<boolean> => {
    if (!client || !client.connected) {
      console.warn("Client not connected, attempting to reconnect...");
      const isConnected = await mqttConnect();
      if (!isConnected) {
        console.error("MQTT Publish failed: Unable to connect");
        return false;
      }
    }

    console.log(topic);
    console.log(message);
    

    return new Promise((resolve, reject) => {
      if (clientRef.current) {
        clientRef.current.publish(
          topic,
          message,
          { qos },
          (error) => {
            if (error) {
              console.error("MQTT Publish error:", error);
              reject(false);
            } else {
              resolve(true);
            }
          }
        );
      } else {
        reject(false);
      }
    });
  };

  // ✅ ฟังก์ชัน Subscribe topic
  const mqttSubscribe = async (
    topic: string,
    qos: 0 | 1 | 2 = 0
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      if (clientRef.current) {
        clientRef.current.subscribe(topic, { qos }, (error) => {
          if (error) {
            console.error("MQTT Subscribe error:", error);
            resolve(false);
          } else {
            setIsSubscribed(true);
            setSubscriptionDetail(topic);
            resolve(true);
          }
        });
      } else {
        resolve(false);
      }
    });
  };

  // ✅ ฟังก์ชัน Unsubscribe topic
  const mqttUnsubscribe = (topic: string): boolean => {
    if (clientRef.current) {
      try {
        clientRef.current.unsubscribe(topic, (error) => {
          if (error) {
            console.error("MQTT Unsubscribe error:", error);
            return false;
          }
          setPayload(null);
          setIsSubscribed(false);
          return true;
        });
      } catch (error) {
        console.error("MQTT Unsubscribe exception:", error);
        return false;
      }
    }
    return false;
  };

  return {
    isConnected,
    connectStatus,
    payload,
    isSubscribed,
    subscriptionDetail,
    mqttConnect,
    mqttDisconnect,
    mqttPublish,
    mqttSubscribe,
    mqttUnsubscribe,
  };
};

export default useMqtt;
