import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";
import AutoUpdateForm from "@/components/page-ui/auto-update/auto-update-form";
import { getFTPFolder } from "@/lib/ftp";
import { getData } from "@/lib/actions";
import { endPontList } from "@/lib/data";

export const metadata: Metadata = {
  title: "Auto Update",
};

export default async function POSConfigPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();
  const folders = await getFTPFolder();

  return (
    <>
      {/* <TitleHeader title="Auto Update" type="fixed" /> */}
      <AutoUpdateForm
        folders={folders}
        configs={configs}
        session={session as Session}
      />
    </>
  );
}
