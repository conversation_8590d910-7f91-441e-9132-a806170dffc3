import Playground from "@/components/page-ui/mqtt/playground-ui/playground";
import TitleHeader from "@/components/ui/title-header";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "MQTT - Playground",
};

export default function MqttPlaygroundPage() {
  return (
    <>
      {/* <TitleHeader title="MQTT - Playground" type="static" /> */}
      <Playground />
    </>
  );
}
