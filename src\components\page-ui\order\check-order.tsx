"use client";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { Textarea } from "@/components/shadcn-ui/textarea";
import { endPontList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { checkOrderSchema, ICheckOrderSchema } from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useState } from "react";
import { checkOrder } from "@/lib/api-service";
import CheckOrderTable from "@/components/table/check-order-table";
import { Loader2 } from "lucide-react";
import FixedContent from "@/components/ui/fixed-content";
import { Session } from "next-auth";
import axios from "axios";
import { endPointToken } from "@/lib/utils";
import { admigLog } from "@/lib/actions";

type Props = {
  session: Session;
  configs: any;
};

export default function CheckOrder({ session, configs }: Props) {
  const isFixProject = configs.isFixProject;

  const {
    control,
    register,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<ICheckOrderSchema>({
    resolver: zodResolver(checkOrderSchema),
    defaultValues: {
      endPoint: isFixProject ? session.user.endPoint : "",
    },
  });

  const [data, setData] = useState<any>();
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);

  const renderSelectProject = () => {
    switch (isFixProject) {
      case true:
        return (
          <Controller
            name="endPoint"
            control={control}
            render={({ field }) => {
              const selectedItem = endPontList.find(
                (item) => item.url === field.value
              );
              return (
                <Select
                  onValueChange={(value) => {
                    field.onChange(value),
                      setValue("endPoint", value),
                      setData(null);
                  }}
                  value={field.value}
                  disabled={isFixProject}
                >
                  <SelectTrigger id="project" aria-label="Select project">
                    <SelectValue placeholder="Select project">
                      {selectedItem ? selectedItem.name : "Select Project"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {endPontList.map((item) => (
                      <SelectItem key={item.id} value={item.url}>
                        {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            }}
          />
        );

      case false:
        return (
          <Select
            onValueChange={(value) => {
              setValue("endPoint", value), setData(null);
            }}
          >
            <SelectTrigger id="project" aria-label="Select project">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {endPontList.map((item) => (
                <SelectItem key={item.id} value={item.url}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
    }
  };

  const validateFormData = (data: ICheckOrderSchema): boolean => {
    let isValid = true;

    if (!data.endPoint) {
      setError("endPoint", {
        type: "server",
        message: "Please select Project.",
      });
      isValid = false;
    }

    if (!data.url) {
      setError("url", {
        type: "server",
        message: "Please enter URL.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: ICheckOrderSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    const { endPoint, url } = data;
    const qrUrl = url.split(/qr\?|qrstatic\?/);
    const checkUrl = qrUrl[1];

    try {
      // const response = await checkOrder(data);
      const response = await axios.get(
        `${endPoint}/api/checkorder?${checkUrl}`,
        {
          headers: {
            Accept: "application/json",
            key: endPointToken(endPoint as string),
          },
        }
      );
      if (response.status === 200) {
        setData(response.data);
      } else {
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        endPoint,
        session?.user?.name || "Anonymous",
        "GET",
        `${endPoint}/api/checkorder?${checkUrl}`,
        "",
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="grid gap-4 grid-cols-12 w-full">
        <FixedContent
          parentClassName="col-span-12 lg:col-span-6 xl:col-span-5"
          childClassName="lg:fixed lg:z-10 lg:w-full"
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            <Card className="bg-sidebar">
              <CardHeader>
                <CardTitle className="text-xl md:text-2xl">
                  Check Order
                </CardTitle>
                <CardDescription>
                  {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6">
                  <div className="grid gap-3">
                    <Label htmlFor="name">Project</Label>
                    {renderSelectProject()}
                    {errors.endPoint && (
                      <p className="text-sm text-red-500">
                        {errors.endPoint.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="url">URL</Label>
                    <Textarea
                      {...register("url")}
                      id="url"
                      name="url"
                      placeholder="Enter URL"
                      className="min-h-32"
                      onChange={() => setData(null)}
                    />
                    {errors.url && (
                      <p className="text-sm text-red-500">
                        {errors.url.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="gap-2 justify-center">
                {/* <Button type="reset" variant={"outline"}>
                  Reset
                </Button> */}
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  <span className="flex items-center justify-center gap-2">
                    {isSubmitting ? (
                      <>
                        <p>Searching...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Search</p>
                      </>
                    )}
                  </span>
                </Button>
              </CardFooter>
            </Card>
          </form>
        </FixedContent>
        <div className="col-span-12 lg:col-span-6 xl:col-span-7 space-y-4">
          <CheckOrderTable isLoading={isSubmitting} data={data} />
        </div>
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
