"use server";

const { Client } = require("basic-ftp");

export async function getFTPFolder() {
  const client = new Client();
  // client.ftp.verbose = true;

  try {
    await client.access({
      host: process.env.FTP_HOST,
      user: process.env.FTP_USERNAME,
      password: process.env.FTP_PASSWORD,
    });

    const list = await client.list(process.env.FTP_FOLDER_PATH);

    const folders = list
      .filter((item: any) => item.isDirectory)
      .map((folder: any) => ({ name: folder.name }));

    return folders;
  } catch (error) {
    console.error("Error accessing FTP:", error);
  } finally {
    client.close();
  }
}
