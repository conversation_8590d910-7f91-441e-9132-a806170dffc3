import mqtt from "mqtt";
import { z } from "zod";

interface Merchant {
  merchantId: number;
  merchantCode: string;
  merchantName: string;
  slug: string;
  thirdPartyId: number;
  thirdPartyType: number;
  tokenKey: string | null;
}

export type MerchantList = {
  merchantList: Merchant[];
};

export type RegistrationInfo = {
  companyName: string;
  dealerCode: string;
  expireDate: string;
  registrationCode: string;
  registrationId: number;
  isPaid: boolean;
  useMA: boolean;
}

export type ILoginSchema = z.infer<typeof loginSchema>;
export const loginSchema = z.object({
  username: z.string({ message: "Please enter your username." }),
  password: z.string({ message: "Please enter your password." }),
  // endPoint: z.string(),
});

export type ICheckOrderSchema = z.infer<typeof checkOrderSchema>;
export const checkOrderSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  url: z.string({ message: "Please enter URL." }),
});

export type ISearchMerchantIdSchema = z.infer<typeof searchMerchantIdSchema>;
export const searchMerchantIdSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  merchantId: z.string({ message: "Please select Merchant ID." }),
});

export type ISearchMenuProfileIdSchema = z.infer<typeof searchMenuProfileIdSchema>;
export const searchMenuProfileIdSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  language: z.string({ message: "Please select language." }),
  merchantId: z.string({ message: "Please select Merchant ID." }),
  menuProfileCode: z.string({ message: "Please select menu profile code." }),
});

export type IClearCacheOrderStatusSchema = z.infer<
  typeof clearCacheOrderStatusSchema
>;
export const clearCacheOrderStatusSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  merchantId: z.string({ message: "Please select Merchant ID." }),
  orderId: z.string({ message: "Please enter Order ID." }),
});

export type IClearCacheMerchantSchema = z.infer<
  typeof clearCacheMerchantSchema
>;
export const clearCacheMerchantSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  merchantId: z.string({ message: "Please select Merchant ID." }),
});

export type IClearCacheAllSchema = z.infer<typeof clearCacheAllSchema>;
export const clearCacheAllSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
});

export type IClearMenuProfileSchema = z.infer<typeof clearMenuProfileSchema>;
export const clearMenuProfileSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  merchantId: z.string({ message: "Please select Merchant ID." }),
  menuProfileCode: z.array(z.string({ message: "Please select menu profile code." })),
});

export type IClearAllMenuProfileSchema = z.infer<typeof clearMenuProfileSchema>;
export const clearAllMenuProfileSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  merchantId: z.string({ message: "Please select Merchant ID." }),
});

export type IMqttConnectionSchema = z.infer<typeof mqttConnectionSchema>;
export const mqttConnectionSchema = z.object({
  protocol: z.string({ message: "Please select Protocol." }),
  host: z.string({ message: "Please enter Host." }),
  port: z.string({ message: "Please enter Port." }),
  clientId: z.string({ message: "Please enter Client ID." }),
  username: z.string({ message: "Please enter username." }),
  password: z.string({ message: "Please enter password." }),
});

export type IMqttSubscriberSchema = z.infer<typeof mqttSubscriberSchema>;
export const mqttSubscriberSchema = z.object({
  topic: z.string({ message: "Please enter topic." }),
  qos: z.string({ message: "Please select QoS." }),
});

export type IMqttPublisherSchema = z.infer<typeof mqttPublisherSchema>;
export const mqttPublisherSchema = z.object({
  topic: z.string({ message: "Please enter topic." }),
  qos: z.string({ message: "Please select QoS." }),
  payload: z.string({ message: "Please enter payload." }),
});

export type IMqttMonitorConnectionSchema = z.infer<
  typeof mqttMonitorConnectionSchema
>;
export const mqttMonitorConnectionSchema = z.object({
  protocol: z.string({ message: "Please select Protocol." }),
  host: z.string({ message: "Please enter Host." }),
  port: z.string({ message: "Please enter Port." }),
  clientId: z.string({ message: "Please enter Client ID." }),
  username: z.string({ message: "Please enter username." }),
  password: z.string({ message: "Please enter password." }),
  topic: z.string({ message: "Please enter topic." }),
});

export type IMqttPublisherCheckSchema = z.infer<typeof mqttPublisherCheckSchema>;
export const mqttPublisherCheckSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  registrationCode: z.string({ message: "Please select merchant." }),
  topic: z.string({ message: "Please enter topic." }),
  qos: z.string({ message: "Please select QoS." }),
  payload: z.string({ message: "Please enter payload." }),
});

export type IMqttCheckStatusConnectionSchema = z.infer<
  typeof mqttCheckStatusConnectionSchema
>;
export const mqttCheckStatusConnectionSchema = z.object({
  protocol: z.string({ message: "Please select Protocol." }),
  host: z.string({ message: "Please enter Host." }),
  port: z.string({ message: "Please enter Port." }),
  clientId: z.string({ message: "Please enter Client ID." }),
  username: z.string({ message: "Please enter username." }),
  password: z.string({ message: "Please enter password." }),
});

export type ISettingConfigSchema = z.infer<typeof settingConfigSchema>;
export const settingConfigSchema = z.object({
  endPoint: z.string({ message: "Please select project." }),
  merchantGroupId: z.string(),
  merchantId: z.array(z.string({ message: "Please select Merchant ID." })),
  envOptions: z.array(
    z.object({
      key: z.string({ message: "Please enter Key." }),
      value: z.string({ message: "Please enter Value." }),
    })
  ),
  isUpdateHQ: z.boolean(),
});

export type IAutoUpdateSchema = z.infer<
  typeof autoUpdateSchema
>;
export const autoUpdateSchema = z.object({
  mqttHostType: z.string({ message: "Please select MQTT Host." }),
  version: z.string({ message: "Please select version." }),
  customer: z.string({ message: "Please select customer." }),
});

export type IMerchantListByProject = z.infer<typeof MerchantListByProject>;
export const MerchantListByProject = z.object({
  endPoint: z.string({ message: "Please select project." }),
});

