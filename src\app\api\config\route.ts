import { NextResponse } from "next/server";
import { unstable_noStore as noStore } from "next/cache";
import { configs } from "@/lib/data";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/auth";

export async function GET() {
  noStore();
  
  // const session = await getServerSession(authOptions);
  // console.log("🚀 ~ GET ~ session:", session)
  // if (!session) {
  //   return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  // }

  try {
    const response = configs.isFixProject
    return NextResponse.json({ isFixProject: response }, { status: 200 });
  } catch (error) {
    console.error("Error fetching data:", error);
    return NextResponse.json(
      { error: "Failed to fetch data" },
      { status: 500 }
    );
  }
}
