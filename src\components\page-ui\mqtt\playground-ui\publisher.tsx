import {
  IMqttPublisherSchema,
  IMqttSubscriberSchema,
  mqttPublisherSchema,
  mqttSubscriberSchema,
} from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useContext } from "react";
import { Controller, useForm } from "react-hook-form";
import { QosOption } from "./playground";
import { Button } from "@/components/shadcn-ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import { Input } from "@/components/shadcn-ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { Loader2 } from "lucide-react";
import { Textarea } from "@/components/shadcn-ui/textarea";

const record = {
  topic: "infogrammer-test",
  qos: "0",
  payload: "",
};

export default function Publisher({ publish }: any) {
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
    watch,
    control,
  } = useForm<IMqttPublisherSchema>({
    resolver: zodResolver(mqttPublisherSchema),
    defaultValues: record,
  });

  const qosOptions = useContext(QosOption);

  const onQosChange = (value: string) => {
    setValue("qos", value);
  };

  const validateFormData = (data: IMqttPublisherSchema): boolean => {
    let isValid = true;

    if (!data.topic) {
      setError("topic", {
        type: "server",
        message: "Please enter topic.",
      });
      isValid = false;
    }

    if (!data.qos) {
      setError("qos", {
        type: "server",
        message: "Please select QoS.",
      });
      isValid = false;
    }

    if (!data.payload) {
      setError("payload", {
        type: "server",
        message: "Please enter payload.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: IMqttPublisherSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    try {
      const { topic, qos, payload } = data;
      const value = {
        topic,
        qos: parseInt(qos),
        payload,
      };
      publish(value);
    } catch (error) {
      console.log("🚀 ~ onSubmit ~ error:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl md:text-2xl">Publisher</CardTitle>
          <CardDescription>
            {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="grid gap-3">
              <Label htmlFor="topic">Topic</Label>
              <Input
                {...register("topic")}
                id="topic"
                name="topic"
                type="text"
                placeholder=""
              />
              {errors.topic && (
                <p className="text-sm text-red-500">{errors.topic.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="qos">QoS</Label>
              <Controller
                name="qos"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      onQosChange(value);
                    }}
                    value={field.value}
                  >
                    <SelectTrigger id="qos" name="qos" aria-label="Select qos">
                      <SelectValue>{field.value || "Select QoS"}</SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {qosOptions.map((item: any) => (
                        <SelectItem key={item.value} value={item.value}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.qos && (
                <p className="text-sm text-red-500">{errors.qos.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="payload">Payload</Label>
              <Textarea
                {...register("payload")}
                id="payload"
                name="payload"
                defaultValue=""
                className="min-h-32"
              />
              {errors.payload && (
                <p className="text-sm text-red-500">{errors.payload.message}</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="gap-2 justify-center">
          <Button type="submit" className={`w-full`} disabled={isSubmitting}>
            <span className="flex items-center justify-center gap-2">
              {isSubmitting ? (
                <>
                  <p>Publishing...</p>
                  <Loader2 className="h-4 w-4 animate-spin" />
                </>
              ) : (
                <>
                  <p>Publish</p>
                </>
              )}
            </span>
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
