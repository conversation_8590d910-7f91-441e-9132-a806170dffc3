"use client";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>es<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { end<PERSON>ontList, languageList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ISearchMenuProfileIdSchema,
  searchMenuProfileIdSchema,
} from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useEffect, useState } from "react";
import { getData } from "@/lib/api-service";
import Combobox from "@/components/ui/combobox";
import MenuProfileDetailTable from "@/components/table/menu-profile-detail-table";
import { Loader2 } from "lucide-react";
import FixedContent from "@/components/ui/fixed-content";
import { Session } from "next-auth";
import axios from "axios";
import { endPointToken } from "@/lib/utils";
import { admigLog } from "@/lib/actions";
import { Input } from "@/components/shadcn-ui/input";

type Props = {
  session: Session;
  configs: any;
};

export default function SearchMerchantID({ session, configs }: Props) {
  const isFixProject = configs.isFixProject;

  const {
    control,
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<ISearchMenuProfileIdSchema>({
    resolver: zodResolver(searchMenuProfileIdSchema),
    defaultValues: {
      endPoint: isFixProject ? session.user.endPoint : "",
    },
  });

  const [data, setData] = useState<any>();
  const [merchant, setMerchant] = useState<any>();
  const [menuProfile, setMenuProfile] = useState<any>();
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);
  const [merchantLoading, setMerchantLoading] = useState<boolean>(false);
  const [menuProfileLoading, setMenuProfileLoading] = useState<boolean>(false);
  const [isSelectedKT, setIsSelectedKT] = useState<boolean>(false);

  const renderSelectProject = () => {
    switch (isFixProject) {
      case true:
        return (
          <Controller
            name="endPoint"
            control={control}
            render={({ field }) => {
              const selectedItem = endPontList.find(
                (item) => item.url === field.value
              );
              return (
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    setValue("endPoint", value);
                    setData(null);
                    setMerchant(null);
                    setMenuProfile(null);
                    const selectedEndPoint = endPontList.find(
                      (endpoint) => endpoint.url === value
                    );
                    if (selectedEndPoint?.name === "API_KT") {
                      setIsSelectedKT(true);
                    } else {
                      setIsSelectedKT(false);
                      findMerchant(value, "merchant/filter");
                    }
                  }}
                  value={field.value}
                  disabled={isFixProject}
                >
                  <SelectTrigger id="project" aria-label="Select project">
                    <SelectValue placeholder="Select project">
                      {selectedItem ? selectedItem.name : "Select Project"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {endPontList.map((item) => (
                      <SelectItem key={item.id} value={item.url}>
                        {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            }}
          />
        );

      case false:
        return (
          <Select
            onValueChange={(value) => {
              setValue("endPoint", value);
              const selectedEndPoint = endPontList.find(
                (endpoint) => endpoint.url === value
              );
              if (selectedEndPoint?.name === "API_KT") {
                setIsSelectedKT(true);
              } else {
                setIsSelectedKT(false);
                findMerchant(value, "merchant/filter");
              }
              setData(null);
              setMerchant(null);
              setMenuProfile(null);
            }}
          >
            <SelectTrigger id="project" aria-label="Select project">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {endPontList.map((item) => (
                <SelectItem key={item.id} value={item.url}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
    }
  };

  const findMerchant = async (endPoint: string, apiPath: string) => {
    setMerchantLoading(true);
    setMerchant(null);
    try {
      // const response = await getData(endPoint, apiPath);
      const response = await axios.get(`${endPoint}/api/${apiPath}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endPoint as string),
        },
      });
      if (response.status === 200) {
        setMerchant(response.data);
        setMerchantLoading(false);
      } else {
        setMerchantLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
    } catch (error) {
      setMerchantLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, can not find merchant id",
        onClose: clearAlert,
      });
    }
  };

  useEffect(() => {
    if (isFixProject) {
      const endPoint = getValues("endPoint");
      if (endPoint) {
        findMerchant(endPoint, "merchant/filter");
      }
    }
  }, [isFixProject, getValues]);

  const findMenuProfileCode = async (
    endPoint: string,
    apiPath: string,
    id?: string
  ) => {
    setMenuProfileLoading(true);
    setMenuProfile(null);
    try {
      // const response = await getData(endPoint as string, apiPath, id);
      const response = await axios.get(`${endPoint}/api/${apiPath}/${id}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endPoint as string),
        },
      });
      if (response.status === 200) {
        setMenuProfile(response.data);
        setMenuProfileLoading(false);
      } else {
        setMenuProfileLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
    } catch {
      setMenuProfileLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, can not find merchant id",
        onClose: clearAlert,
      });
    }
  };

  const validateFormData = (data: ISearchMenuProfileIdSchema): boolean => {
    let isValid = true;

    if (!data.endPoint) {
      setError("endPoint", {
        type: "server",
        message: "Please select Project.",
      });
      isValid = false;
    }

    if (!data.language) {
      setError("language", {
        type: "server",
        message: "Please select Language.",
      });
      isValid = false;
    }

    if (!data.merchantId) {
      setError("merchantId", {
        type: "server",
        message: "Please select Merchant ID.",
      });
      isValid = false;
    }

    if (!data.menuProfileCode) {
      setError("menuProfileCode", {
        type: "server",
        message: "Please select Menu Profile Code.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: ISearchMenuProfileIdSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    const { endPoint, language, merchantId, menuProfileCode } = data;
    let url = `MenuProfile/${merchantId}/translate?menuprofilecode=${encodeURIComponent(
      menuProfileCode
    )}&language=${language}`;

    try {
      // const response = await getData(endPoint, url);
      const response = await axios.get(`${endPoint}/api/${url}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endPoint as string),
        },
      });
      if (response.status === 200) {
        setData(response.data);
      } else {
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        endPoint,
        session?.user?.name || "Anonymous",
        "GET",
        `${endPoint}/api/${url}`,
        "",
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="grid gap-4 grid-cols-12 w-full relative">
        <FixedContent
          parentClassName="col-span-12 lg:col-span-6 xl:col-span-5"
          childClassName="lg:fixed lg:z-10 lg:w-full"
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl md:text-2xl">
                  Search by Menu Profile ID
                </CardTitle>
                <CardDescription>
                  {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6">
                  <div className="grid gap-3">
                    <Label htmlFor="project">Project</Label>
                    {renderSelectProject()}
                    {errors.endPoint && (
                      <p className="text-sm text-red-500">
                        {errors.endPoint.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="language">Language</Label>
                    <Select
                      onValueChange={(value) => setValue("language", value)}
                    >
                      <SelectTrigger id="language" aria-label="Select language">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        {languageList.map((item) => (
                          <SelectItem key={item.id} value={item.locale}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.language && (
                      <p className="text-sm text-red-500">
                        {errors.language.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="merchantId">Merchant ID</Label>
                    {isSelectedKT ? (
                      <Input
                        {...register("merchantId")}
                        id="merchantId"
                        name="merchantId"
                        type="text"
                        placeholder="Enter Merchant ID"
                        // onChange={() => setIsClear(null)}
                      />
                    ) : (
                      <Combobox
                        title="Merchant ID"
                        listData={merchant}
                        valueKey="merchantId"
                        nameKey="merchantName"
                        slugKey="slug"
                        loading={merchantLoading}
                        showValueWithName={true}
                        onValueChange={(selectedValue) => {
                          setValue("merchantId", selectedValue),
                            findMenuProfileCode(
                              getValues("endPoint"),
                              "MenuProfiles/Only",
                              selectedValue
                            );
                        }}
                      />
                    )}
                    {merchant && errors.merchantId && (
                      <p className="text-sm text-red-500">
                        {errors.merchantId.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="menuProfileCode">Menu Profile Code</Label>
                    {isSelectedKT ? (
                      <Input
                        {...register("menuProfileCode")}
                        id="menuProfileCode"
                        name="menuProfileCode"
                        type="text"
                        placeholder="Enter Menu Profile Code"
                        // onChange={() => setIsClear(null)}
                      />
                    ) : (
                      <Combobox
                        title="Menu Profile Code"
                        listData={menuProfile}
                        valueKey="menuProfileCode"
                        nameKey="menuProfileCode"
                        loading={menuProfileLoading}
                        onValueChange={(selectedValue) => {
                          setValue("menuProfileCode", selectedValue);
                        }}
                      />
                    )}
                    {menuProfile &&
                      menuProfile.length !== 0 &&
                      errors.menuProfileCode && (
                        <p className="text-sm text-red-500">
                          {errors.menuProfileCode.message}
                        </p>
                      )}
                    {menuProfile?.length === 0 && (
                      <p className="text-sm text-red-500">
                        No Menu Profile Code!
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="gap-2 justify-center">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  <span className="flex items-center justify-center gap-2">
                    {isSubmitting ? (
                      <>
                        <p>Searching...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Search</p>
                      </>
                    )}
                  </span>
                </Button>
              </CardFooter>
            </Card>
          </form>
        </FixedContent>
        <div className="col-span-12 lg:col-span-6 xl:col-span-7 space-y-4">
          <MenuProfileDetailTable isLoading={isSubmitting} data={data} />
        </div>
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
