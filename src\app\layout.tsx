import type { Metada<PERSON> } from "next";
import "./globals.css";
import { anuphan } from "@/lib/fonts";
import { ThemeProvider } from "@/components/ui/theme-provider";
import SessionProvider from "@/components/sessionProvider";
import { getServerSession } from "next-auth";

export const metadata: Metadata = {
  title: {
    template: "%s | INFOGRAMMER",
    default: "INFOGRAMMER",
  },
  description: "Generated by INFOGRAMMER",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await getServerSession();

  return (
    <html lang="en" className="!scroll-smooth" suppressHydrationWarning>
      <body className={`${anuphan.className} antialiased`}>
        <SessionProvider session={session}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            {children}
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
