import crypto from 'crypto';

let m_sKey = process.env.DATA_PROTECTION_KEY || '';

export function Encrypt(strObj: string): string {
    if (!strObj) return '';

    let strValue = '';
    if (m_sKey) {
        if (m_sKey.length < 16) {
            m_sKey += 'XXXXXXXXXXXXXXXX'.substring(0, 16 - m_sKey.length);
        } else if (m_sKey.length > 16) {
            m_sKey = m_sKey.substring(0, 16);
        }

        const byteKey = Uint8Array.from(Buffer.from(m_sKey.substring(0, 8), 'utf8'));
        const byteVector = Uint8Array.from(Buffer.from(m_sKey.substring(8), 'utf8'));
        const cipher = crypto.createCipheriv('des-cbc', byteKey, byteVector);

        let encrypted = cipher.update(strObj, 'utf8', 'base64');
        encrypted += cipher.final('base64');

        strValue = encrypted;
    } else {
        strValue = strObj;
    }

    return strValue;
}

export function Decrypt(strObj: string): string {
  if (!strObj) return '';

  let strValue = '';

  if (m_sKey) {
      if (m_sKey.length < 16) {
          m_sKey += 'XXXXXXXXXXXXXXXX'.substring(0, 16 - m_sKey.length);
      } else if (m_sKey.length > 16) {
          m_sKey = m_sKey.substring(0, 16);
      }

      const byteKey = Uint8Array.from(Buffer.from(m_sKey.substring(0, 8), 'utf8'));
      const byteVector = Uint8Array.from(Buffer.from(m_sKey.substring(8), 'utf8'));

      let byteData: Buffer;
      try {
          byteData = Buffer.from(strObj, 'base64');
      } catch {
          return strObj; // กรณีไม่ใช่ Base64 ก็คืนค่าเดิม
      }

      try {
          const decipher = crypto.createDecipheriv('des-cbc', byteKey, byteVector);
          let decrypted = decipher.update(byteData as any, 'base64', 'utf8');
          decrypted += decipher.final('utf8');
          strValue = decrypted;
      } catch {
          // ถ้าการถอดรหัสล้มเหลว ให้คืนค่า strObj
          strValue = strObj;
      }
  } else {
      strValue = strObj;
  }

  return strValue;
}

export function Encode64(message: string): string {
  try {
    const messageByte = Buffer.from(message, "ascii");
    return messageByte.toString("base64");
  } catch (error) {
    console.error("Error encoding base64:", error);
    return ""; // Return empty string if decoding fails
  }
}

export function Decode64(message: string): string {
  try {
    const messageByte = Buffer.from(message, "base64");
    return messageByte.toString("ascii");
  } catch (error) {
    console.error("Error decoding base64:", error);
    return ""; // Return empty string if decoding fails
  }
}
