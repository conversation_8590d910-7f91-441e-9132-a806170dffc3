import { Spinner } from "@/components/ui/spinner";
import { Loader2 } from "lucide-react";

export default function Loading() {
  return (
    <div className="flex flex-1 items-center justify-center rounded-lg h-full">
      <div className="flex items-center justify-center gap-2 text-center animate-bounce">
        <Loader2 className="text-primary animate-spin" />
        <h3 className="text-2xl font-semibold tracking-tight ">Loading... </h3>
      </div>
    </div>
  );
}
