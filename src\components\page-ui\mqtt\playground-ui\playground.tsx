"use client";
import React, { createContext, useEffect, useState } from "react";
import mqtt, { MqttClient } from "mqtt";
import PlaygroundConnection from "./connection";
import Subscriber from "./subscriber";
import Publisher from "./publisher";
import PlaygroundReceiver from "./receiver";

export const QosOption = createContext([]);
const qosOption = [
  {
    label: "0",
    value: 0,
  },
  {
    label: "1",
    value: 1,
  },
  {
    label: "2",
    value: 2,
  },
];

export default function Playground() {
  const [client, setClient] = useState<MqttClient | null>(null);
  const [isSubed, setIsSub] = useState(false);
  const [payload, setPayload] = useState({});
  const [connectStatus, setConnectStatus] = useState("Connect");

  const mqttConnect = (host: string, mqttOption: mqtt.IClientOptions) => {
    setConnectStatus("Connecting");
    const conn = mqtt.connect(host, mqttOption);
    setClient(conn);
  };

  useEffect(() => {
    if (client) {
      // https://github.com/mqttjs/MQTT.js#event-connect
      client.on("connect", () => {
        setConnectStatus("Connected");
        console.log("connection successful");
      });

      // https://github.com/mqttjs/MQTT.js#event-error
      client.on("error", (err) => {
        console.error("Connection error: ", err);
        client.end();
      });

      // https://github.com/mqttjs/MQTT.js#event-reconnect
      client.on("reconnect", () => {
        setConnectStatus("Reconnecting");
      });

      // https://github.com/mqttjs/MQTT.js#event-message
      client.on("message", (topic, message) => {
        const payload = { topic, message: message.toString() };
        setPayload(payload);
        
      });
    }
  }, [client]);

  // disconnect
  // https://github.com/mqttjs/MQTT.js#mqttclientendforce-options-callback
  const mqttDisconnect = () => {
    if (client) {
      try {
        client.end(false, () => {
          setConnectStatus("Connect");
          console.log("disconnected successfully");
        });
      } catch (error) {
        console.log("disconnect error:", error);
      }
    }
  };

  // publish message
  // https://github.com/mqttjs/MQTT.js#mqttclientpublishtopic-message-options-callback
  const mqttPublish = (context: { topic: any; qos: any; payload: any }) => {
    if (client) {
      // topic, QoS & payload for publishing message
      const { topic, qos, payload } = context;
      client.publish(topic, payload, { qos }, (error) => {
        if (error) {
          console.log("Publish error: ", error);
        }
      });
    }
  };

  const mqttSub = (subscription: { topic: any; qos: any }) => {
    if (client) {
      // topic & QoS for MQTT subscribing
      const { topic, qos } = subscription;
      // subscribe topic
      // https://github.com/mqttjs/MQTT.js#mqttclientsubscribetopictopic-arraytopic-object-options-callback

      client.subscribe(topic, { qos }, (error) => {
        if (error) {
          console.log("Subscribe to topics error", error);
          return;
        }
        console.log(`Subscribe to topics: ${topic}`);
        setIsSub(true);
      });
    }
  };

  // unsubscribe topic
  // https://github.com/mqttjs/MQTT.js#mqttclientunsubscribetopictopic-array-options-callback
  const mqttUnSub = (subscription: { topic: any; qos: any }) => {
    if (client) {
      const { topic, qos } = subscription;
      client.unsubscribe(topic, { qos } as any, (error) => {
        if (error) {
          console.log("Unsubscribe error", error);
          return;
        }
        console.log(`unsubscribed topic: ${topic}`);
        setIsSub(false);
      });
    }
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="grid gap-4 grid-cols-12 w-full">
        <div className="col-span-12 lg:col-span-4">
          <PlaygroundConnection
            connect={mqttConnect}
            disconnect={mqttDisconnect}
            connectBtn={connectStatus}
          />
        </div>
        <QosOption.Provider value={qosOption as any}>
          <div className="col-span-12 lg:col-span-4">
            <Subscriber sub={mqttSub} unSub={mqttUnSub} isSubscribe={isSubed} />
          </div>
          <div className="col-span-12 lg:col-span-4">
            <Publisher publish={mqttPublish} />
          </div>
        </QosOption.Provider>
        <div className="col-span-12">
          <PlaygroundReceiver payload={payload} />
        </div>
      </div>
    </div>
  );
}
