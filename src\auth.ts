import { NextAuthOptions } from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60,
  },
  pages: {
    signIn: '/login',
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",

      credentials: {
        // endPoint: { label: "EndPoint", type: "text" },
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },

      authorize: async (credentials) => {
        // const trimmedUrl = credentials?.endPoint.split("/api")[0];
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        const dataSession = {
          id: 1,
          name: credentials.username,
          endPoint: '',
        };
        return dataSession;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as number;
        token.endPoint = user.endPoint;
      }
      return token;
    },
    async session({ session, token }) {
      if (session) {
        session.user.id = token.id;
        session.user.endPoint = token.endPoint;
      }
      return session;
    },
  },
};
