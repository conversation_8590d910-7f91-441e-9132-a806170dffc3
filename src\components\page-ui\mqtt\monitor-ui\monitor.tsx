"use client";
import React, { createContext, useEffect, useState } from "react";
import mqtt, { MqttClient } from "mqtt";
import MonitorConnection from "../monitor-ui/connection";
import MonitorReceiver from "./receiver";
import { getDbData } from "@/lib/api-service";
import { decodeJSONMessage } from "@/lib/utils";
import { Decrypt } from "@/lib/encode";

export const QosOption = createContext([]);

export default function Monitor() {
  const [client, setClient] = useState<MqttClient | null>(null);
  const [topic, setTopic] = useState("");
  const [hostData, setHostData] = useState<any>([]);
  const [isConnect, setIsConnect] = useState<boolean>(false);
  const [payload, setPayload] = useState({});
  const [connectStatus, setConnectStatus] = useState("Connect");
  const [dbTopics, setDbTopics] = useState<any>([]);
  const [topicLoading, setTopicLoading] = useState<boolean>(false);

  const fetchTopics = async (params: any) => {
    setTopicLoading(true);
    const { host, username } = params;
    try {
      const response = await getDbData("topic", host, username);
      if (response.status === 200) {
        setDbTopics(response.data);
        setTopicLoading(false);
      } else {
        setTopicLoading(false);
        // setShowAlert({
        //   type: "warning",
        //   detail: "Search failed, please try again.",
        //   onClose: clearAlert,
        // });
        return;
      }
    } catch {
      setTopicLoading(false);
      // setShowAlert({
      //   type: "error",
      //   detail: "Something went wrong, can not find merchant id",
      //   onClose: clearAlert,
      // });
    }
  };

  const mqttConnect = (
    host: string,
    mqttOption: mqtt.IClientOptions,
    topic: string
  ) => {
    const parsedUrl = new URL(host);
    const { username } = mqttOption;
    const params = {
      host: parsedUrl.host,
      username: username,
    };
    setConnectStatus("Connecting");
    setTopic(topic);
    setHostData(params);
    const conn = mqtt.connect(host, mqttOption);
    setClient(conn);
  };

  useEffect(() => {
    if (client) {
      client.on("connect", async () => {
        setConnectStatus("Connected");
        setIsConnect(true);
        await fetchTopics(hostData);
        console.log("connection successful");

        client.subscribe(topic, (err) => {
          if (err) {
            console.error("Subscription error:", err);
            return;
          }
        });
      });

      client.on("error", (err) => {
        console.error("Connection error: ", err);
        client.end();
        setIsConnect(false);
      });

      client.on("reconnect", () => {
        // setConnectStatus("Reconnecting");
        client.end();
        setIsConnect(false);
      });

      // https://github.com/mqttjs/MQTT.js#event-message
      client.on("message", (topic, message) => {
        const payload = {
          topic,
          message: decodeJSONMessage(message.toString()),
        };
        setPayload(payload);
      });
    }
  }, [client]);

  const mqttDisconnect = () => {
    if (client) {
      try {
        client.end(false, () => {
          setConnectStatus("Connect");
          setIsConnect(false);
          setDbTopics([]);
          console.log("disconnected successfully");
        });
      } catch (error) {
        console.log("disconnect error:", error);
      }
    }
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="grid gap-4 grid-cols-12 w-full">
        <div className="col-span-12">
          <MonitorConnection
            connect={mqttConnect}
            disconnect={mqttDisconnect}
            connectBtn={connectStatus}
          />
        </div>
        {isConnect && (
          <div className="col-span-12">
            <MonitorReceiver
              payload={payload}
              dbTopics={dbTopics}
              loading={topicLoading}
              triggerRefreshData={() => fetchTopics(hostData)}
            />
          </div>
        )}
      </div>
    </div>
  );
}
