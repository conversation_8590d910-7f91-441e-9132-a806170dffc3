generator client {
  provider   = "prisma-client-js"
  engineType = "library"
}

datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_PRISMA_URL")
  directUrl = env("POSTGRES_URL_NON_POOLING")
}

model Host {
  id        Int      @id @default(autoincrement())
  url       String   @unique
  username  String
  isSync    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  topics    Topic[]
}

model Topic {
  id         Int       @id @default(autoincrement())
  name       String    @unique
  isSync     Boolean   @default(false)
  createdAt  DateTime  @default(now())
  hostId     Int
  updatedAt  DateTime  @updatedAt
  messages   Message[]
  host       Host      @relation(fields: [hostId], references: [id])
}

model Message {
  id         Int      @id @default(autoincrement())
  isSync     Boolean  @default(true)
  topicId    Int
  content    Json
  // identifier String   @unique
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  topic      Topic    @relation(fields: [topicId], references: [id])
}
