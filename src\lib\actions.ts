import axios from "axios";
import xml2js from "xml2js";
// const { Client } = require("basic-ftp") 
import { endPointToken } from "./utils";
import { ILoginSchema } from "./types";

// export const createSoapRequest = (username: string, password: string) => {
//   return `<?xml version="1.0" encoding="utf-8"?>
//     <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
//       <soap12:Body>
//         <UserAuthorize xmlns="http://restaurantprogram.net/">
//           <sUserName>${username}</sUserName>
//           <sPassword>${password}</sPassword>
//         </UserAuthorize>
//       </soap12:Body>
//     </soap12:Envelope>`;
// };

// export async function login(data: ILoginSchema) {
//   const { username, password } = data;
//   const soapRequest = createSoapRequest(username, password);

//   try {
//     console.log("loginnnnnnnnn");

//     const response = await axios.post(
//       "http://infosvr.thaiddns.com/license/RegistrationService.asmx",
//       soapRequest,
//       {
//         headers: {
//           "Content-Type": "application/soap+xml; charset=utf-8",
//           SOAPAction: "http://restaurantprogram.net/UserAuthorize",
//         },
//       }
//     );

//     const responseText = response.data;

//     const parser = new xml2js.Parser({ explicitArray: false });
//     const result = await parser.parseStringPromise(responseText);

//     const userAuthorizeResult =
//       result["soap:Envelope"]["soap:Body"]["UserAuthorizeResponse"][
//         "UserAuthorizeResult"
//       ];

//     return {
//       username,
//       password,
//       isCorrect: userAuthorizeResult === "true",
//     };
//   } catch (error) {
//     console.error("Error during SOAP request:", error);
//     return {
//       username,
//       password,
//       isCorrect: false,
//     };
//   }
// }

export async function login(data: ILoginSchema) {
  const api = `${process.env.SUPER_ADMIN_API_URL}/auth/login`;
  try {
    const response = await fetch(api, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`Error! : ${error}`);
  }
}

function createSoapRequestForGetAllPackage(): string {
  return `<?xml version="1.0" encoding="utf-8"?>
  <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
      <GetAllPackage xmlns="http://restaurantprogram.net/" />
    </soap:Body>
  </soap:Envelope>`;
}

export async function fetchAllPackages() {
  const soapRequest = createSoapRequestForGetAllPackage();

  try {
    const response = await fetch(
      "http://infosvr.thaiddns.com/license/RegistrationService.asmx",
      {
        method: "POST",
        headers: {
          "Content-Type": "text/xml; charset=utf-8",
          SOAPAction: "http://restaurantprogram.net/GetAllPackage",
        },
        body: soapRequest,
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log("SOAP Response:", responseText);

    // Parse the XML response using xml2js
    const parser = new xml2js.Parser({
      explicitArray: false,
      mergeAttrs: true,
    });
    const result = await parser.parseStringPromise(responseText);

    console.log("Parsed JSON:", result);

    // Extract the result
    const getAllPackageResult =
      result["soap:Envelope"]["soap:Body"]["GetAllPackageResponse"][
        "GetAllPackageResult"
      ];
    console.log("Extracted Result:", getAllPackageResult);

    // Process the result as needed
    return getAllPackageResult;
  } catch (error) {
    console.error("Error during SOAP request:", error);
    throw error;
  }
}

export async function getData(endPoint: string, apiPath: string) {
  try {
    const response = await axios.get(`${endPoint}/api/${apiPath}`, {
      headers: {
        Accept: "application/json",
        key: endPointToken(endPoint),
      },
    });

    return response;
  } catch (error) {
    console.error("Error while fetching data:", error);
    throw error;
  }
}

export async function getDataById(
  endPoint: string,
  apiPath: string,
  id: string
) {
  try {
    const response = await axios.get(`${endPoint}/api/${apiPath}/${id}`, {
      headers: {
        Accept: "application/json",
        key: endPointToken(endPoint),
      },
    });

    return response;
  } catch (error) {
    console.error("Error while fetching data:", error);
    throw error;
  }
}

export async function updateConfigPOS(endPoint: string, data: any) {
  try {
    const { MerchantId, ...restData } = data;

    if (Array.isArray(MerchantId) && MerchantId.length > 1) {
      const postPromises = MerchantId.map(async (id: number) => {
        const postData = { ...restData, MerchantId: id };
        const response = await axios.post(
          `${endPoint}/api/config/pos`,
          postData,
          {
            headers: {
              Accept: "application/json",
              key: endPointToken(endPoint),
            },
          }
        );
        return response;
      });

      const responses = await Promise.all(postPromises);
      return responses;
    } else {
      const response = await axios.post(`${endPoint}/api/config/pos`, data, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endPoint),
        },
      });

      return response;
    }
  } catch (error) {
    console.error("Error while fetching data:", error);
    throw error;
  }
}

export async function admigLog(
  url: string,
  username: string,
  method: string,
  endpoint: string,
  reqdata: string,
  response: string,
  code: string
) {
  const now = new Date();
  const formattedDate =
    now.getFullYear() +
    "-" +
    String(now.getMonth() + 1).padStart(2, "0") +
    "-" +
    String(now.getDate()).padStart(2, "0") +
    " " +
    String(now.getHours()).padStart(2, "0") +
    ":" +
    String(now.getMinutes()).padStart(2, "0") +
    ":" +
    String(now.getSeconds()).padStart(2, "0");
  const log = {
    RequestDate: formattedDate,
    Username: username,
    HttpMethod: method,
    Endpoint: endpoint,
    RequestData: reqdata,
    Response: response,
    StatusCode: code.toString(),
  };
  try {
    await axios.post(`https://infoeasy.cc:7702/api/adminLogging`, log, {
      headers: {
        Accept: "application/json",
        key: process.env.API_KEY_7702,
      },
    });
  } catch (error) {
    console.error("Error while fetching data:", error);
    throw error;
  }
}

// export async function getFTPFolder() {
//   const client = new Client();
//   client.ftp.verbose = true;

//   try {
//     await client.access({
//       host: process.env.FTP_HOST,
//       user: process.env.FTP_USERNAME,
//       password: process.env.FTP_PASSWORD,
//     });

//     const list = await client.list(process.env.FTP_FOLDER_PATH);

//     const folders = list
//       .filter((item: any) => item.isDirectory)
//       .map((folder: any) => folder.name);

//     return folders;
//   } catch (error) {
//     console.error("Error accessing FTP:", error);
//   } finally {
//     client.close();
//   }
// }
