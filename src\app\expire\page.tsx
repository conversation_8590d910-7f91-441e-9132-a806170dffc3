import { But<PERSON> } from "@/components/shadcn-ui/button";
import { authOptions } from "@/auth";
import { getServerSession } from "next-auth/next";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function Expire() {
  const session = await getServerSession(authOptions);
  if (session) {
    redirect("/dashboard");
  }

  return (
    <div className="h-screen flex flex-col w-full justify-center items-center gap-2">
      <div className="text-left bg-primary p-10 h-16 w-16 rounded-full -translate-y-10">
        <h3 className="text-5xl font-bold tracking-tight -translate-x-20">
          401
        </h3>
        <h4 className="text-2xl">Unauthorized</h4>
      </div>
      <Link href={"/login"}>
        <Button className="mt-2 uppercase" variant={"secondary"}>
          Go To Login
        </Button>
      </Link>
    </div>
  );
}
