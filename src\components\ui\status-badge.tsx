import { cn } from "@/lib/utils";
import React from "react";

type Props = {
  type:
    | "default"
    | "warning"
    | "danger"
    | "success"
    | "primary"
    | "secondary"
    | "custom";
  title: string;
  className?: string;
};

type BadgeProps = {
  className?: string;
  children: React.ReactNode;
};

function Badge({ className, children }: BadgeProps) {
  return (
    <div
      className={cn(
        "whitespace-nowrap px-2 rounded-md flex justify-center items-center cursor-default",
        className
      )}
    >
      <p className="text-xs font-medium text-white -translate-y-0.5">
        {children}
      </p>
    </div>
  );
}

export default function StatusBadge({ type, title, className }: Props) {
  const renderBadge = () => {
    switch (type) {
      case "default":
        return <Badge>{title}</Badge>;
      case "warning":
        return <Badge className="bg-amber-600">{title}</Badge>;
      case "danger":
        return <Badge className="bg-red-600">{title}</Badge>;
      case "success":
        return <Badge className="bg-green-600">{title}</Badge>;
      case "primary":
        return <Badge className="bg-primary">{title}</Badge>;
      case "secondary":
        return (
          <Badge className="bg-muted-foreground dark:bg-zinc-900">
            {title}
          </Badge>
        );
      case "custom":
        return <Badge className={className}>{title}</Badge>;
    }
  };

  return renderBadge();
}
