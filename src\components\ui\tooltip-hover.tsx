import {
  Toolt<PERSON>,
  <PERSON><PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/shadcn-ui/tooltip";

type Props = {
  children: React.ReactNode;
  content: any;
  position?: "top" | "right" | "bottom" | "left";
};

export default function TooltipHover({
  children,
  content,
  position = "top",
}: Props) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent side={position}>{content}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
