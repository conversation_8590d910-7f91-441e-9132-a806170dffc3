import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import { Input } from "@/components/shadcn-ui/input";
import { JSONMessage } from "@/lib/utils";
import { Textarea } from "@/components/shadcn-ui/textarea";
import { Button } from "@/components/shadcn-ui/button";
import { RefreshCcw } from "lucide-react";

export type MessagePayload = {
  topic: string;
  message: any;
};

type Props = {
  sub: any;
  unSub: any;
  isPublishCheckSubscribe: boolean | null;
  publishCheckSubDetail: string | null;
  isConnected: boolean | null;
  payload: MessagePayload | null;
  isMerchantChanged: boolean | null;
};

export default function ResultStatus({
  sub,
  unSub,
  isPublishCheckSubscribe,
  publishCheckSubDetail,
  isConnected,
  payload,
  isMerchantChanged,
}: Props) {
  const [resultStatusSub, setResultStatusSub] = useState<{
    topic: string;
    qos: number;
  } | null>(null);

  const [payloadMessageSub, setPayloadMessageSub] = useState<MessagePayload[]>(
    []
  );
  const [merchantChanged, setMerchantChanged] = useState<boolean | null>(null);
  const [isInitialFirstPayload, setIsInitialFisrtPayload] =
    useState<boolean>(true);

  const modifiedTopic = publishCheckSubDetail?.replace(
    /\/checkstatus$/,
    "/resultcheckstatus"
  );

  const handleSubscribe = async () => {
    try {
      const value = {
        topic: modifiedTopic as string,
        // topic: "clearcache/merchant",
        qos: 0,
      };
      setResultStatusSub(value);
      handleCheckFirstPayload();
      sub(value);
    } catch (error) {
      console.log("🚀 ~ onSubscribe ~ error:", error);
    }
  };

  const handleUnsub = () => {
    if (publishCheckSubDetail) {
      unSub(resultStatusSub);
    } else {
      unSub(null);
    }
  };

  const handleCheckFirstPayload = () => {
    if (payload && isInitialFirstPayload) {
      setPayloadMessageSub([]);
      setIsInitialFisrtPayload(false);
    }
  };

  useEffect(() => {
    if (payload) {
      setPayloadMessageSub((prevMessages) => {
        return [...prevMessages, { ...payload }];
      });
    } else {
      setPayloadMessageSub([]);
    }

    if (isPublishCheckSubscribe && isConnected) {
      handleSubscribe();
    } else if (isPublishCheckSubscribe === false && isConnected === false) {
      console.log("unsubbb");
      setResultStatusSub({
        topic: "",
        qos: 0,
      });
      handleUnsub();
    }
  }, [isPublishCheckSubscribe, isConnected, payload]);

  const handleResetPayload = () => {
    setPayloadMessageSub([]);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between gap-1">
        <CardTitle className="text-xl md:text-2xl">Result Status</CardTitle>
        <Button
          onClick={() => handleResetPayload()}
          variant={"ghost"}
          size={"icon"}
        >
          <RefreshCcw className="size-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6">
          <div className="grid gap-3">
            <Label>{resultStatusSub?.topic}</Label>
            <Textarea
              value={
                payloadMessageSub
                  .map((msg) => JSONMessage(msg.message))
                  .join("\n\n") || ""
              }
              rows={25}
              disabled={!isConnected}
              readOnly
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
