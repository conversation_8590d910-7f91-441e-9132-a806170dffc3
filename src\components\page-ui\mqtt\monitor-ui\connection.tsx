"use client";
import {
  IMqttConnectionSchema,
  IMqttMonitorConnectionSchema,
  mqttConnectionSchema,
  mqttMonitorConnectionSchema,
} from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Button } from "@/components/shadcn-ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { Loader2 } from "lucide-react";
import { Input } from "@/components/shadcn-ui/input";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import mqtt from "mqtt";
import { cn } from "@/lib/utils";
import FixedContent from "@/components/ui/fixed-content";
import { initialMQTTConnectionUAT } from "@/lib/data";

type ConnectionProps = {
  connect: any;
  disconnect: any;
  connectBtn: string;
};

export default function MonitorConnection({
  connect,
  disconnect,
  connectBtn,
}: ConnectionProps) {
  const {
    register,
    setValue,
    getValues,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setError,
    control,
  } = useForm<IMqttMonitorConnectionSchema>({
    resolver: zodResolver(mqttMonitorConnectionSchema),
    defaultValues: {
      ...initialMQTTConnectionUAT,
      topic: "#",
    },
  });

  const protocol = watch("protocol");
  const isConnect = connectBtn === "Connected";

  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);

  const onProtocolChange = (value: string) => {
    setValue("protocol", value);
  };

  const validateFormData = (data: IMqttMonitorConnectionSchema): boolean => {
    let isValid = true;

    if (!data.protocol) {
      setError("protocol", {
        type: "server",
        message: "Please select Protocol.",
      });
      isValid = false;
    }

    if (!data.host) {
      setError("host", {
        type: "server",
        message: "Please enter Host.",
      });
      isValid = false;
    }

    if (!data.clientId) {
      setError("clientId", {
        type: "server",
        message: "Please enter Client ID.",
      });
      isValid = false;
    }

    if (!data.port) {
      setError("port", {
        type: "server",
        message: "Please enter Port.",
      });
      isValid = false;
    }

    if (!data.username) {
      setError("username", {
        type: "server",
        message: "Please enter username.",
      });
      isValid = false;
    }

    if (!data.password) {
      setError("password", {
        type: "server",
        message: "Please enter password.",
      });
      isValid = false;
    }

    if (!data.topic) {
      setError("topic", {
        type: "server",
        message: "Please enter topic.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: IMqttMonitorConnectionSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    try {
      const { protocol, host, clientId, port, username, password, topic } =
        data;
      const url = `${protocol}://${host}:${port}`;
      const options: mqtt.IClientOptions = {
        clientId,
        username,
        password,
        clean: true,
        reconnectPeriod: 1000, // ms
        connectTimeout: 30 * 1000, // ms
      };
      connect(url, options, topic);
    } catch (error) {
      console.log("🚀 ~ onSubmit ~ error:", error);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        {!isConnect && (
          <CardHeader>
            <CardTitle className="text-xl md:text-2xl">Connection</CardTitle>
          </CardHeader>
        )}
        <CardContent className={cn(isConnect ? "mt-5" : "")}>
          {isConnect ? (
            <div className="flex flex-col md:flex-row md:items-center gap-2 items-start justify-between">
              <div className="grid grid-cols-1 gap-2 items-start truncate xl:flex md:items-center">
                <div className="flex gap-2 items-center">
                  <h3 className="text-base md:text-lg font-semibold">Connect :</h3>
                  <div className="px-2 py-1 rounded-md border-b border-green-600">
                    <p className="text-base md:text-lg">{`${getValues(
                      "protocol"
                    )}://${getValues("host")}:${getValues("port")}`}</p>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <h3 className="text-base md:text-lg font-semibold">Client ID :</h3>
                  <div className="px-2 py-1 rounded-md border-b border-green-600">
                    <p className="text-base md:text-lg">{`${getValues(
                      "clientId"
                    )}`}</p>
                  </div>
                </div>
                <div className="flex gap-2 items-center">
                  <h3 className="text-base md:text-lg font-semibold">Topic :</h3>
                  <div className="px-2 py-1 rounded-md border-b border-green-600">
                    <p className="text-base md:text-lg">{`${getValues(
                      "topic"
                    )}`}</p>
                  </div>
                </div>
              </div>
              <Button
                type="button"
                variant={"secondary"}
                onClick={handleDisconnect}
              >
                Disconnect
              </Button>
            </div>
          ) : (
            <div className="grid gap-6">
              <div className="grid gap-3 grid-cols-12">
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="protocol">Protocol</Label>
                    <Controller
                      name="protocol"
                      control={control}
                      render={({ field }) => (
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            onProtocolChange(value);
                          }}
                          value={field.value}
                        >
                          <SelectTrigger
                            id="protocol"
                            name="protocol"
                            aria-label="Select protocol"
                          >
                            <SelectValue>
                              {field.value || "Select protocol"}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ws">ws</SelectItem>
                            <SelectItem value="wss">wss</SelectItem>
                            <SelectItem value="mqtt">mqtt</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.protocol && (
                      <p className="text-sm text-red-500">
                        {errors.protocol.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="host">Host</Label>
                    <Input
                      {...register("host")}
                      id="host"
                      name="host"
                      type="text"
                      placeholder=""
                    />
                    {errors.host && (
                      <p className="text-sm text-red-500">
                        {errors.host.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="port">Port</Label>
                    <Input
                      {...register("port")}
                      id="port"
                      name="port"
                      type="number"
                      placeholder=""
                    />
                    {errors.port && (
                      <p className="text-sm text-red-500">
                        {errors.port.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="clientId">Client ID</Label>
                    <Input
                      {...register("clientId")}
                      id="clientId"
                      name="clientId"
                      type="text"
                      placeholder=""
                    />
                    {errors.clientId && (
                      <p className="text-sm text-red-500">
                        {errors.clientId.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      {...register("username")}
                      id="username"
                      name="username"
                      type="text"
                      placeholder=""
                    />
                    {errors.username && (
                      <p className="text-sm text-red-500">
                        {errors.username.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      {...register("password")}
                      id="password"
                      name="password"
                      type="password"
                      placeholder=""
                    />
                    {errors.password && (
                      <p className="text-sm text-red-500">
                        {errors.password.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Label htmlFor="topic" className=" whitespace-nowrap">
                      Subscribe Topic
                    </Label>
                    <Input
                      {...register("topic")}
                      id="topic"
                      name="topic"
                      type="text"
                      placeholder=""
                    />
                    {errors.topic && (
                      <p className="text-sm text-red-500">
                        {errors.topic.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <div className="grid gap-3">
                    <Button
                      type="submit"
                      className={`${
                        isConnect
                          ? "bg-green-600 hover:bg-green-700"
                          : " bg-primary"
                      } w-full mt-5`}
                      disabled={isSubmitting}
                    >
                      <span className="flex items-center justify-center gap-2">
                        {isSubmitting ? (
                          <>
                            <p>Connecting...</p>
                            <Loader2 className="h-4 w-4 animate-spin" />
                          </>
                        ) : (
                          <>
                            <p>{connectBtn}</p>
                          </>
                        )}
                      </span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </form>
  );
}
