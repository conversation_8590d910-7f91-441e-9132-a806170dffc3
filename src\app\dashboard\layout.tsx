import Nav from "@/components/navigation-ui/nav";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // const session = await getServerSession(authOptions);
  // if (!session) {
  //   redirect("/expire");
  // }

  return (
    // <Navigation>
    //   <main className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6 mt-14">
    //     {children}
    //   </main>
    // </Navigation>
    <Nav>{children}</Nav>
  );
}
