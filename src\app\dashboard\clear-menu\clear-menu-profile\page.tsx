import { authOptions } from "@/auth";
import ClearMenuProfile from "@/components/page-ui/clear-menu/clear-menu-profile";
import { getConfig } from "@/lib/api-service";
import { getServerSession, Session } from "next-auth";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Clear Menu Profile",
};

export default async function ClearMenuProfilePage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();

  return (
    <>
      {/* <div className="flex items-center">
        <h1 className="text-lg font-semibold md:text-2xl uppercase">
          Clear Menu Profile
        </h1>
      </div> */}
      <ClearMenuProfile configs={configs} session={session as Session} />
    </>
  );
}
