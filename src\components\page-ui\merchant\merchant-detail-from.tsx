"use client";
import {
    <PERSON>,
    CardContent,
    CardDescription,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON><PERSON><PERSON>,
    Card<PERSON>itle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import React, { useEffect, useState } from "react";
import { TableRowIdle, TableRowSkeleton } from "../../table/table-row";
import { <PERSON>rollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import { Trash } from "lucide-react";
import { Switch } from "@/components/shadcn-ui/switch";
import AlertModal from "../../ui/alert-modal";
import axios from "axios";
import { endPointToken } from "@/lib/utils";
import FixedContent from "@/components/ui/fixed-content";
import { AlertBar, AlertProps } from "../../ui/alert-bar";
import { ISearchMerchantIdSchema } from "@/lib/types";
import { But<PERSON> } from "@/components/shadcn-ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuRadioGroup,
    DropdownMenuRadioItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/shadcn-ui/dropdown-menu";
import { admigLog } from "@/lib/actions";
import { Input } from "../../shadcn-ui/input";
import { Form } from "react-hook-form";
import { Textarea } from "../../shadcn-ui/textarea";
import { formatDate } from "@/lib/utils";

type Props = {
    isLoading: boolean;
    data: any;
    endpoint: string;
    onSearch: (data: ISearchMerchantIdSchema) => void;
    merchantId: string;
    session: any;
};

export default function MerchatDetailTable({ isLoading,
    data,
    endpoint,
    onSearch,
    merchantId,
    session,
}: Props) {
    console.log(data)
    return (
        <div className="grid grid-cols-12 gap-4">
            <Card className="col-span-12 lg:col-span-12">
                <CardHeader>
                    <CardTitle className="text-xl md:text-2xl">
                        รายละเอียด Merchant
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <ScrollArea className="pr-2">
                        <form onSubmit={(e) => e.preventDefault()}>
                            <div className="grid grid-cols-12 gap-2">
                                <div className="col-span-4">
                                    <Label htmlFor="merchantId">MerchantId</Label>
                                    <Input
                                        id="MerchantId"
                                        value={data?.merchantId || ""}
                                        placeholder="MerchantId"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="MerchantCode">MerchantCode</Label>
                                    <Input
                                        id="MerchantCode"
                                        value={data?.merchantCode || ""}
                                        placeholder="MerchantCode"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="MerchantName">MerchantName</Label>
                                    <Input
                                        id="MerchantName"
                                        value={data?.merchantName || ""}
                                        placeholder="MerchantName"
                                        readOnly
                                    />
                                </div>
                                

                                <div className="col-span-4">
                                    <Label htmlFor="Slug">Slug</Label>
                                    <Input
                                        id="Slug"
                                        value={data?.slug || ""}
                                        placeholder="Slug"
                                        readOnly
                                    />
                                </div>
                                
                                <div className="col-span-4">
                                    <Label htmlFor="ThirdPartyId">ThirdPartyId</Label>
                                    <Input
                                        id="ThirdPartyId"
                                        value={data?.thirdPartyId || "0"}
                                        placeholder="ThirdPartyId"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="RegisterId">RegisterId</Label>
                                    <Input
                                        id="RegisterId"
                                        value={data?.registerId || ""}
                                        placeholder="RegisterId"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="MerchantGroupId">MerchantGroupId</Label>
                                    <Input
                                        id="MerchantGroupId"
                                        value={data?.merchantGroupId || ""}
                                        placeholder="MerchantGroupId"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="IsHQMerchant">IsHQMerchant</Label>
                                    <Input
                                        id="IsHQMerchant"
                                        value={data?.isHqmerchant ? "True" : "False"}
                                        placeholder="IsHQMerchant"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="Active">Active</Label>
                                    <Input
                                        id="Active"
                                        value={data?.active ? "True" : "False"}
                                        placeholder="Active"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="Email">Email</Label>
                                    <Input
                                        id="Email"
                                        value={data?.email || ""}
                                        placeholder="Email"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="Longitude">Longitude</Label>
                                    <Input
                                        id="Longitude"
                                        value={data?.longitude || ""}
                                        placeholder="Longitude"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-4">
                                    <Label htmlFor="Latitude">Latitude</Label>
                                    <Input
                                        id="Latitude"
                                        value={data?.latitude || ""}
                                        placeholder="Latitude"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-12">
                                    <Label htmlFor="FullAddress">FullAddress</Label>
                                    <Input
                                        id="FullAddress"
                                        value={data?.fullAddress || ""}
                                        placeholder="FullAddress"
                                        readOnly
                                    />
                                </div>
                                <div className="col-span-12">
                                    <Label htmlFor="MerchantDescription">Description</Label>
                                    <Textarea
                                        id="MerchantDescription"
                                        value={data?.merchantDescription || ""}
                                        placeholder="MerchantDescription"
                                        disabled
                                    />
                                </div>
                            </div>
                        </form>
                    </ScrollArea>
                </CardContent>
            </Card>
        </div>
    )
}


