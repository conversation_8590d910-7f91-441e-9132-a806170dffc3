import ClearCacheMerchant from "@/components/page-ui/clear-cache/clear-cache-merchant";
import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Clear Cache by Merchant ID",
};

export default async function ClearCacheMerchantPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();
  
  return (
    <>
      {/* <TitleHeader title="Clear Cache" type="static" /> */}
      <ClearCacheMerchant configs={configs} session={session as Session}/>
    </>
  );
}
