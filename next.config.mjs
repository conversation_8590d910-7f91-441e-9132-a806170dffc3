/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: 'standalone',
  reactStrictMode: true,
  env: {
    API_KEY_TRUNK: process.env.API_KEY_TRUNK,
    API_KEY_KT: process.env.API_KEY_KT,
    API_KEY_7702: process.env.API_KEY_7702,
    API_KEY_LUCKY_KOUEN: process.env.API_KEY_LUCKY_KOUEN,
    API_KEY_OTHER: process.env.API_KEY_OTHER,
    API_KEY_TEENOI: process.env.API_KEY_TEENOI,
    API_KEY_TEENOI_2: process.env.API_KEY_TEENOI_2,
    API_KEY_WEBORDERING: process.env.API_KEY_WEBORDERING,
    API_KEY_LOCALHOST: process.env.API_KEY_LOCALHOST,
    API_KEY_GONE: process.env.API_KEY_GONE,
    BASE_URL: process.env.BASE_URL,
    STORAGE_URL: process.env.STORAGE_URL,
    SUPER_ADMIN_API_URL: process.env.SUPER_ADMIN_API_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    IS_FIX_PROJECT: process.env.IS_FIX_PROJECT,
    POSTGRES_PRISMA_URL: process.env.POSTGRES_PRISMA_URL,
    POSTGRES_URL_NON_POOLING: process.env.POSTGRES_URL_NON_POOLING,
    DATA_PROTECTION_KEY: process.env.DATA_PROTECTION_KEY,
    FTP_HOST: process.env.FTP_HOST,
    FTP_USERNAME: process.env.FTP_USERNAME,
    FTP_PASSWORD: process.env.FTP_PASSWORD,
    FTP_FOLDER_PATH: process.env.FTP_FOLDER_PATH,
    DB_CONNECTION_113: process.env.DB_CONNECTION_113,
    DB_CONNECTION_TENCENT: process.env.DB_CONNECTION_TENCENT,
    DB_CONNECTION_EASYORDER: process.env.DB_CONNECTION_EASYORDER,
    DB_CONNECTION_EASYORDERGONE: process.env.DB_CONNECTION_EASYORDERGONE,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "infoeasy.cc",
        port: "9098",
        pathname: "/file/**",
      },
      {
        protocol: "https",
        hostname: "easyorder.asia",
        port: "3010",
        pathname: "/file/**",
      },
      {
        protocol: "https",
        hostname: "easyorder.asia",
        port: "3031",
        pathname: "/file/**",
      },
    ],
  },
};

export default nextConfig;
