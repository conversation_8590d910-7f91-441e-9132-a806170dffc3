"use client";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON><PERSON>Big, CircleX, Loader2 } from "lucide-react";
import React, { useEffect, useState } from "react";

interface Props {
  loading?: boolean;
  isSuccess?: boolean | null | undefined;
  successMessage?: string;
  errorMessage?: string;
  loadingMessage?: string;
}

export default function CardActionStatus({
  loading,
  isSuccess,
  successMessage,
  errorMessage,
  loadingMessage,
}: Props) {
  const [visible, setVisible] = useState(true);
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    if (loading) {
      setVisible(true); // Show the card
      setProgress(100); // Reset progress to 100%
      return; // Exit early if loading
    }

    if (isSuccess !== null && isSuccess !== undefined) {
      setVisible(true); // Show the card
      setProgress(100); // Reset progress to 100%

      const totalTime = 5000; // 5 seconds
      const stepTime = 50; // Update progress every 50ms
      const stepSize = 100 / (totalTime / stepTime);

      const interval = setInterval(() => {
        setProgress((prev) => (prev > 0 ? prev - stepSize : 0));
      }, stepTime);

      const timer = setTimeout(() => {
        setVisible(false); // Hide the card after 5 seconds
      }, totalTime);

      return () => {
        clearTimeout(timer);
        clearInterval(interval);
      };
    }
  }, [loading, isSuccess]);

  if (!visible) return null;

  return (
    <div
      className={cn(
        "w-full rounded-md py-2 border lg:w-[calc(100%-250px)] xl:w-[calc(100%-500px)] transition-all",
        loading
          ? "border-gray-500 bg-gray-500/10"
          : isSuccess === true
          ? "border-primary bg-primary/10 text-primary"
          : isSuccess === false
          ? "border-red-600 bg-red-600/10 text-red-500"
          : "border-none bg-none text-transparent"
      )}
    >
      {loading || isSuccess === true ? (
        <div className="flex relative flex-col gap-1 px-[1px]">
          <div className="flex justify-between items-center gap-2 px-3">
            <div className="flex justify-start items-center gap-3">
              {loading && (
                <>
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <p className="text-lg font-semibold">{loadingMessage}</p>
                </>
              )}
              {isSuccess === true && (
                <>
                  <CircleCheckBig className="h-6 w-6" />
                  <p className="text-lg font-semibold">{successMessage}</p>
                </>
              )}
            </div>
          </div>
          {!loading && isSuccess && (
            <div
              className="h-0.5 bottom-0 absolute bg-primary rounded-full transition-all translate-y-2"
              style={{ width: `${progress}%` }}
            />
          )}
        </div>
      ) : isSuccess === false ? (
        <div className="flex flex-col gap-1 px-[1px]">
          <div className="flex justify-between px-3 items-center gap-2">
            <div className="flex justify-start items-center gap-3">
              <CircleX className="h-6 w-6" />
              <p className="text-lg font-semibold">{errorMessage}</p>
            </div>
          </div>
          {!loading && isSuccess === false && (
            <div
              className="h-0.5 bg-red-600 rounded-full transition-all translate-y-2"
              style={{ width: `${progress}%` }}
            />
          )}
        </div>
      ) : null}
    </div>
  );
}
