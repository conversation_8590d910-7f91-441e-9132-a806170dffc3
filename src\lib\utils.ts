import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { endPontList } from "./data";
import { Decrypt } from "./encode";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function endPointToken(endPoint: string) {
  const endPointObj = endPontList.find((ep) => ep.url === endPoint);
  return endPointObj?.token;
}

export function convertDate(dateString: string | Date) {
  const date = new Date(dateString);
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  };

  return date.toLocaleString("th-TH", options);
}

export function decodeJSONMessage(message: string) {
  try {
    const decryptedMessage = Decrypt(message);
    return JSON.parse(decryptedMessage);
  } catch (error) {
    console.warn("Message is not encrypted or is already decrypted");
    return message;
  }
}

export function JSONMessage(message: string | JSON | any) {
  try {
    if (typeof message === "object") {
      message = JSON.stringify(message);
    }
    return JSON.stringify(JSON.parse(message), null, 2);
  } catch (error) {
    // console.warn("Message is not encrypted or is already decrypted");
    return message;
  }
}

// Function to parse .env file
export const parseEnvFile = (text: string) => {
  const format = text
    .split("\n")
    .map((line) => line.split("="))
    .filter(([key, value]) => key && value)
    .map(([key, value]) => ({
      key: key.trim(),
      value: value.trim().replace(/^"|"$/g, ""),
    }));

  return format;
};

// Function to parse JSON file
export const parseJsonFile = (text: string) => {
  const jsonData = JSON.parse(text);
  return Object.keys(jsonData).map((key) => ({
    key: key,
    value: jsonData[key],
  }));
};

// Function to parse CSV file
export const parseCsvFile = (text: string) => {
  const lines = text.split("\n");
  return lines.map((line) => {
    const [key, value] = line.split(",");
    return { key: key.trim(), value: value.trim() };
  });
};

export const getInitials = (name: string | undefined) => {
  if (!name) return ""; // กรณีไม่มีชื่อ

  const words = name.split(" "); // แยกชื่อเป็นคำ
  if (words.length > 1) {
    // ถ้ามีมากกว่า 1 คำ ให้ดึงตัวอักษรแรกของคำสองคำแรก
    return words
      .map((word) => word[0]?.toUpperCase()) // ดึงตัวอักษรแรกและแปลงเป็นตัวพิมพ์ใหญ่
      .slice(0, 2) // ดึงตัวอักษรจากคำสองคำแรก
      .join(""); // รวมกลับเป็น string
  } else {
    // ถ้ามีคำเดียว ให้ดึง 2 ตัวแรก
    return name.slice(0, 2).toUpperCase();
  }
};

export function formatDate(dateString: string): string {
  const date = new Date(dateString);

  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const yearBE = date.getFullYear() + 543;

  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");

  return `${day}/${month}/${yearBE} ${hours}:${minutes}`;
}

export function convertOADateToDateString(oaDate: number): string {
  const date = new Date((oaDate - 25569) * 86400000);
  return date.toISOString().split("T")[0]; // Format as 'YYYY-MM-DD'
}
