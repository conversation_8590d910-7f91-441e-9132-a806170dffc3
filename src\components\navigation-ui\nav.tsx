"use client";
import { Separator } from "@/components/shadcn-ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/shadcn-ui/sidebar";
import { AppSidebar } from "@/components/navigation-ui/app-slidebar";
import NavBreadcrumb from "./nav-breadcrumb";
import { usePathname } from "next/navigation";

interface Props {
  children: React.ReactNode;
}

export default function Nav({ children }: Props) {
  const pathname = usePathname();

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex z-50 bg-background w-full sticky top-0 h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1 size-6" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <NavBreadcrumb />
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4 w-dvw md:w-full">
          {pathname === "/dashboard" ? (
            children
          ) : (
            <div className="min-h-dvh flex-1 rounded-xl border bg-sidebar/50 md:min-h-min p-4 md:p-8">
              {children}
            </div>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
