import {
  IMqttPublisherCheckSchema,
  IMqttPublisherSchema,
  IMqttSubscriberSchema,
  mqttPublisherCheckSchema,
  mqttPublisherSchema,
  mqttSubscriberSchema,
} from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Button } from "@/components/shadcn-ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import { Input } from "@/components/shadcn-ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { Loader2 } from "lucide-react";
import { Textarea } from "@/components/shadcn-ui/textarea";
import { QosOption } from "./check-status";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import axios from "axios";
import { endPointToken, JSONMessage } from "@/lib/utils";
import { endPontList } from "@/lib/data";
import Combobox from "@/components/ui/combobox";
import { Decrypt } from "@/lib/encode";
import { format } from "date-fns-tz";

const record = {
  topic: "",
  qos: "0",
  payload: "",
};

interface Props {
  publish: any;
  sub: any;
  unSub: any;
  isConnected: boolean | null;
  triggerMerchantChange: (trigger: boolean) => void;
}

export default function PublishCheck({
  publish,
  sub,
  unSub,
  isConnected,
  triggerMerchantChange,
}: Props) {
  const {
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
    watch,
    control,
  } = useForm<IMqttPublisherCheckSchema>({
    resolver: zodResolver(mqttPublisherCheckSchema),
    defaultValues: record,
  });

  const [merchant, setMerchant] = useState<any>();
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);
  const [merchantLoading, setMerchantLoading] = useState<boolean>(false);

  useEffect(() => {
    if (isConnected === false) {
    }
  }, [isConnected]);

  const findMerchant = async (endPoint: string, apiPath: string) => {
    setMerchantLoading(true);
    setMerchant(null);
    try {
      // const response = await getData(endPoint, apiPath);
      const response = await axios.get(`${endPoint}/api/${apiPath}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endPoint as string),
        },
      });
      if (response.status === 200) {
        setMerchant(response.data);
        setMerchantLoading(false);
      } else {
        setMerchantLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
    } catch (error) {
      setMerchantLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, can not find merchant id",
        onClose: clearAlert,
      });
    }
  };

  const handleSetPayload = async (merchantId?: string) => {
    const timeZone = "Asia/Bangkok";
    const currentDateTime = format(
      new Date(),
      "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
      { timeZone }
    );

    const payload = {
      MQTTDateTime: currentDateTime,
      ViewDetail: null,
      MQTTMessage: null,
    };

    setValue("payload", JSON.stringify(payload));

    if (merchantId) {
      const selectMerchant = merchant.find(
        (m: any) => m.merchantId === parseInt(merchantId)
      );

      await handleUnsub();

      setValue(
        "topic",
        `${Decrypt(selectMerchant.registrationCode)}/checkstatus`
      );

      await handleSubscribe();
    }
  };

  const validateFormData = (data: IMqttPublisherCheckSchema): boolean => {
    let isValid = true;

    if (!data.topic) {
      setError("topic", {
        type: "server",
        message: "Please enter topic.",
      });
      isValid = false;
    }

    if (!data.qos) {
      setError("qos", {
        type: "server",
        message: "Please select QoS.",
      });
      isValid = false;
    }

    if (!data.payload) {
      setError("payload", {
        type: "server",
        message: "Please enter payload.",
      });
      isValid = false;
    }

    return isValid;
  };

  const handleSubscribe = async () => {
    try {
      const value = {
        topic: getValues("topic"),
        qos: 0,
      };
      sub(value);
    } catch (error) {
      console.log("🚀 ~ handleSubscribe ~ error:", error);
    }
  };

  const onPublish = async (data: IMqttPublisherCheckSchema) => {
    console.log("onPublish...");

    if (!validateFormData(data)) {
      return;
    }

    await handleSetPayload();

    try {
      const { topic, qos } = data;
      const value = {
        topic,
        qos: parseInt(qos),
        payload: Decrypt(getValues("payload")),
      };
      publish(value);
    } catch (error) {
      console.log("🚀 ~ onSubmit ~ error:", error);
    }
  };

  const handleUnsub = async () => {
    const currentTopic = getValues("topic");
    console.log("🚀 ~ handleUnsub ~ currentTopic:", currentTopic)

    // ตรวจสอบว่ามี topic เก่าหรือไม่ ถ้าไม่มีจะไม่ unsubscribe
    if (currentTopic) {
      const value = {
        topic: currentTopic,
        qos: 0,
      };
      unSub(value);
    }
  };

  return (
    <form onSubmit={handleSubmit(onPublish)}>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl md:text-2xl">
            Publish Check Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="grid gap-3">
              <Label htmlFor="regisCode">Project</Label>
              <Select
                onValueChange={(value) => {
                  setValue("endPoint", value),
                    findMerchant(value, "merchant/filter"),
                    setMerchant(null),
                    setValue("topic", ""),
                    setValue("payload", "");
                }}
                disabled={!isConnected}
              >
                <SelectTrigger id="project" aria-label="Select project">
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  {endPontList.map((item) => (
                    <SelectItem key={item.id} value={item.url}>
                      {item.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.endPoint && (
                <p className="text-sm text-red-500">
                  {errors.endPoint.message}
                </p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="regisCode">Merchant</Label>
              <Combobox
                title="Merchant"
                listData={merchant}
                valueKey="merchantId"
                nameKey="merchantName"
                slugKey="slug"
                loading={merchantLoading}
                showResetButton={true}
                showValueWithName={true}
                onValueChange={(selectedValue) => {
                  setValue("registrationCode", selectedValue),
                    handleSetPayload(selectedValue),
                    triggerMerchantChange(true);
                }}
                disabled={!isConnected}
              />
              {merchant && errors.registrationCode && (
                <p className="text-sm text-red-500">
                  {errors.registrationCode.message}
                </p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="topic">Topic</Label>
              <Input
                {...register("topic")}
                id="checkTopic"
                name="topic"
                type="text"
                placeholder="{registrationCode}/checkstatus"
                disabled
              />
              {errors.topic && (
                <p className="text-sm text-red-500">{errors.topic.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="payload">Payload</Label>
              <Textarea
                {...register("payload")}
                id="payload"
                name="payload"
                value={JSONMessage(getValues("payload")) || ""}
                className="min-h-32"
                disabled
              />
              {errors.payload && (
                <p className="text-sm text-red-500">{errors.payload.message}</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="gap-2 justify-center">
          <Button
            type="submit"
            className={`w-full`}
            disabled={isSubmitting || !isConnected}
          >
            <span className="flex items-center justify-center gap-2">
              {isSubmitting ? (
                <>
                  <p>Publishing...</p>
                  <Loader2 className="h-4 w-4 animate-spin" />
                </>
              ) : (
                <>
                  <p>Publish</p>
                </>
              )}
            </span>
          </Button>
        </CardFooter>
      </Card>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </form>
  );
}
