import { Skeleton } from "../shadcn-ui/skeleton";
import { TableCell, TableRow } from "../shadcn-ui/table";

export function TableRowIdle({
  title,
  colSpan,
}: {
  title: string;
  colSpan: number;
}) {
  return (
    <TableRow>
      <TableCell
        colSpan={colSpan}
        className="text-center italic text-muted-foreground"
      >
        {title}
      </TableCell>
    </TableRow>
  );
}

export function TableRowSkeleton({ numCol }: { numCol: number }) {
  const colCount = numCol >= 1 ? numCol : 1;

  return (
    <TableRow>
      {Array.from({ length: colCount }).map((_, index) => (
        <TableCell
          key={index}
          className="lg:text-center italic text-muted-foreground"
        >
          <Skeleton className="h-4 w-full" />
        </TableCell>
      ))}
    </TableRow>
  );
}

export function TableRowError({
  title,
  colSpan,
}: {
  title: string;
  colSpan: number;
}) {
  return (
    <TableRow>
      <TableCell
        colSpan={colSpan}
        className="text-center"
      >
        {title}
      </TableCell>
    </TableRow>
  );
}
