"use client";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardContent,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/shadcn-ui/table";
import React, { useState } from "react";
import { TableRowIdle, TableRowSkeleton } from "./table-row";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import { Switch } from "@/components/shadcn-ui/switch";
import AlertModal from "../ui/alert-modal";
import axios from "axios";
import { AlertBar, AlertProps } from "../ui/alert-bar";
import { IMerchantListByProject, MerchantListByProject } from "@/lib/types";
import { Button } from "@/components/shadcn-ui/button";
import { admigLog } from "@/lib/actions";
import { object } from "zod";

type Props = {
    isLoading: boolean;
    data: any;
    endpoint: string;
    session: any;
};

export default function MerchantConfigTable({
    isLoading,
    data,
    endpoint,
    session,
}: Props) {
    const [isAlertOpen, setIsAlertOpen] = useState(false);
    const [isLoadingModal, setIsLoadingModal] = useState(false);
    const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
    const [isLoadingTable, setIsLoadingTable] = useState(false);

    //** ######################### ✅ Render ข้อมูล Merchant List ######################### */
    const rendermerchantlist = () => {
        if(!data) return null;
        return Object.entries(data).map(([key, item], i) => {
            return (
                <React.Fragment key={i}>
                    <TableRow>
                        <TableCell className="text-center">
                            <input type="checkbox" className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        </TableCell>
                    </TableRow>
                </React.Fragment>
            )
        });
    }



    return (
        <div className="grid grid-cols-12 gap-4">
            <Card className="col-span-12 lg:col-span-12">
                <CardHeader>
                    <CardTitle className="text-xl md:text-2xl">
                        รายการ Merchant
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <ScrollArea className="mt-6 border whitespace-nowrap rounded-md">
                        <Table>
                            <TableHeader className="bg-default">
                                <TableRow>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center rounded-tl-md w-[10%]">
                                        <input type="checkbox" id="merchant-all" className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                                        MerchantId
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[20%] truncate">
                                        MerchantName
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                                        Slug
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                                        Status
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {rendermerchantlist()}
                            </TableBody>
                            <ScrollBar orientation="horizontal" />
                        </Table>
                    </ScrollArea>
                </CardContent>
            </Card>
        </div>
    )
}