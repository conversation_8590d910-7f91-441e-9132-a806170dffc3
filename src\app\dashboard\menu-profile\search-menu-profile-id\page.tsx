import SearchMenuProfileID from "@/components/page-ui/menu-profile/search-menu-profile-id";
import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Search by Menu Profile ID",
};

export default async function SearchMenuPorfileIdPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();

  return (
    <>
      {/* <TitleHeader title="Menu Profile" type="fixed" /> */}
      <SearchMenuProfileID configs={configs} session={session as Session} />
    </>
  );
}
