"use client";
import { IMqttConnectionSchema, mqttConnectionSchema } from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { Button } from "@/components/shadcn-ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { Loader2 } from "lucide-react";
import { Input } from "@/components/shadcn-ui/input";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import mqtt from "mqtt";
import { initialMQTTConnectionUAT } from "@/lib/data";

type ConnectionProps = {
  connect: any;
  disconnect: any;
  connectBtn: string;
};



export default function PlaygroundConnection({
  connect,
  disconnect,
  connectBtn,
}: ConnectionProps) {
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setError,
    control,
  } = useForm<IMqttConnectionSchema>({
    resolver: zodResolver(mqttConnectionSchema),
    defaultValues: initialMQTTConnectionUAT,
  });

  const protocol = watch("protocol");

  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);

  const onProtocolChange = (value: string) => {
    setValue("protocol", value);
    // setValue("port", value === "mqtt" ? 1883 : value === "wss" ? 8084 : 8083);
  };

  const validateFormData = (data: IMqttConnectionSchema): boolean => {
    let isValid = true;

    if (!data.protocol) {
      setError("protocol", {
        type: "server",
        message: "Please select Protocol.",
      });
      isValid = false;
    }

    if (!data.host) {
      setError("host", {
        type: "server",
        message: "Please enter Host.",
      });
      isValid = false;
    }

    if (!data.clientId) {
      setError("clientId", {
        type: "server",
        message: "Please enter Client ID.",
      });
      isValid = false;
    }

    if (!data.port) {
      setError("port", {
        type: "server",
        message: "Please enter Port.",
      });
      isValid = false;
    }

    if (!data.username) {
      setError("username", {
        type: "server",
        message: "Please enter username.",
      });
      isValid = false;
    }

    if (!data.password) {
      setError("password", {
        type: "server",
        message: "Please enter password.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: IMqttConnectionSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    try {
      const { protocol, host, clientId, port, username, password } = data;
      // const url = `${protocol}://${host}:${port}/mqtt`;
      const url = `${protocol}://${host}:${port}`;
      const options: mqtt.IClientOptions = {
        clientId,
        username,
        password,
        clean: true,
        reconnectPeriod: 1000, // ms
        connectTimeout: 30 * 1000, // ms
        // log: console.log.bind(console),
      };
      connect(url, options);
    } catch (error) {
      console.log("🚀 ~ onSubmit ~ error:", error);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  const handleDisconnect = () => {
    disconnect();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl md:text-2xl">Connection</CardTitle>
          <CardDescription>
            {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="grid gap-3">
              <Label htmlFor="protocol">Protocol</Label>
              <Controller
                name="protocol"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      onProtocolChange(value);
                    }}
                    value={field.value}
                  >
                    <SelectTrigger
                      id="protocol"
                      name="protocol"
                      aria-label="Select protocol"
                    >
                      <SelectValue>
                        {field.value || "Select protocol"}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ws">ws</SelectItem>
                      <SelectItem value="wss">wss</SelectItem>
                      <SelectItem value="mqtt">mqtt</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.protocol && (
                <p className="text-sm text-red-500">
                  {errors.protocol.message}
                </p>
              )}
            </div>
            <div className="grid gap-3 grid-cols-12">
              <div className="col-span-7">
                <div className="grid gap-3">
                  <Label htmlFor="host">Host</Label>
                  <Input
                    {...register("host")}
                    id="host"
                    name="host"
                    type="text"
                    placeholder=""
                  />
                  {errors.host && (
                    <p className="text-sm text-red-500">
                      {errors.host.message}
                    </p>
                  )}
                </div>
              </div>
              <div className="col-span-5">
                <div className="grid gap-3">
                  <Label htmlFor="port">Port</Label>
                  <Input
                    {...register("port")}
                    id="port"
                    name="port"
                    type="number"
                    placeholder=""
                  />
                  {errors.port && (
                    <p className="text-sm text-red-500">
                      {errors.port.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
            <div className="grid gap-3">
              <Label htmlFor="clientId">Client ID</Label>
              <Input
                {...register("clientId")}
                id="clientId"
                name="clientId"
                type="text"
                placeholder=""
              />
              {errors.clientId && (
                <p className="text-sm text-red-500">
                  {errors.clientId.message}
                </p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="username">Username</Label>
              <Input
                {...register("username")}
                id="username"
                name="username"
                type="text"
                placeholder=""
              />
              {errors.username && (
                <p className="text-sm text-red-500">
                  {errors.username.message}
                </p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="password">Password</Label>
              <Input
                {...register("password")}
                id="password"
                name="password"
                type="password"
                placeholder=""
              />
              {errors.password && (
                <p className="text-sm text-red-500">
                  {errors.password.message}
                </p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="gap-2 justify-center">
          {/* <Button type="reset" variant={"outline"}>
                  Reset
                </Button> */}
          <Button
            type="submit"
            className={`${
              connectBtn == "Connected"
                ? "bg-green-600 hover:bg-green-700"
                : " bg-primary"
            } w-full`}
            disabled={isSubmitting}
          >
            <span className="flex items-center justify-center gap-2">
              {isSubmitting ? (
                <>
                  <p>Connecting...</p>
                  <Loader2 className="h-4 w-4 animate-spin" />
                </>
              ) : (
                <>
                  <p>{connectBtn}</p>
                </>
              )}
            </span>
          </Button>
          <Button
            type="button"
            className="w-full"
            variant={"secondary"}
            onClick={handleDisconnect}
          >
            Disconnect
          </Button>
        </CardFooter>
      </Card>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </form>
  );
}
