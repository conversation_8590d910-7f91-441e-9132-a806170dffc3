import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function DELETE() {
  try {
    // Step 1: Delete all messages
    await prisma.message.deleteMany();

    // Step 2: Delete all topics
    await prisma.topic.deleteMany();

    return NextResponse.json(
      { message: "Delete all topics and messages success" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting topics and messages:", error);
    return NextResponse.json(
      { error: "Failed to delete topics and messages" },
      { status: 500 }
    );
  }
}
