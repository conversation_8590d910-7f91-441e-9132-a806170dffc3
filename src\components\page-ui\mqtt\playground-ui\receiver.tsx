import {
  Card,
  CardContent,
  CardDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import StatusBadge from "@/components/ui/status-badge";
import TooltipHover from "@/components/ui/tooltip-hover";
import React, { useEffect, useState } from "react";

type Message = {
  topic: string;
  message: string;
  datetime: string;
  sync: boolean;
};

type Props = {
  payload: Message;
};

export default function PlaygroundReceiver({ payload }: Props | any) {
  const [messages, setMessages] = useState<Record<string, Message[]>>({});

  useEffect(() => {
    if (payload.topic) {
      setMessages((prevMessages) => {
        const newMessages = { ...prevMessages };
        if (!newMessages[payload.topic]) {
          newMessages[payload.topic] = [];
        }
        newMessages[payload.topic].push({
          ...payload,
          datetime: new Date().toLocaleString(),
          sync: false,
        });
        return newMessages;
      });
    }
  }, [payload]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl md:text-2xl">Receiver</CardTitle>
        <CardDescription></CardDescription>
      </CardHeader>
      <CardContent>
        <div className="w-full mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Object.keys(messages).length === 0 && (
              <div className="bg-secondary dark:bg-background border rounded-md p-4 flex flex-col gap-4 w-full">
                <div className="flex items-center justify-between">
                  <span className="font-medium underline italic text-primary-foreground/50">
                    topic
                  </span>
                </div>
                <pre className="whitespace-normal italic text-primary-foreground/50 text-sm">
                  message...
                </pre>
              </div>
            )}
            {Object.keys(messages)
              .reverse()
              .map((topic, i) => (
                <div
                  key={i}
                  className="custom-scrollbar bg-secondary h-[400px] overflow-y-scroll dark:bg-background border rounded-md p-4 gap-4 flex flex-col"
                >
                  <div className="flex items-center justify-between">
                    <TooltipHover content={<p>{topic}</p>}>
                      <h5 className="font-medium underline text-base md:text-lg truncate cursor-default">
                        {topic}
                      </h5>
                    </TooltipHover>
                  </div>
                  {messages[topic].map((item, j) => (
                    <React.Fragment key={j}>
                      <div className="flex items-center justify-center gap-2">
                        <pre className="whitespace-pre-wrap break-words text-sm w-full">
                          {item.message}
                        </pre>
                      </div>
                      <div className="flex flex-col">
                        <div className="flex items-center justify-end gap-2">
                          <span className="text-xs text-muted-foreground">
                            {item.datetime}
                          </span>
                        </div>
                        <hr className="bg-zinc-700 dark:bg-zinc-100 h-0.5 my-2" />
                      </div>
                    </React.Fragment>
                  ))}
                </div>
              ))}
          </div>
          {/* </ScrollArea> */}
        </div>
        {/* <ScrollBar orientation="horizontal" /> */}
      </CardContent>
    </Card>
  );
}
