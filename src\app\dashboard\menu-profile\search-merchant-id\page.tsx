import SearchMerchantID from "@/components/page-ui/menu-profile/search-merchant-id";
import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Search by Menu Profile ID",
};

export default async function SearchMerchantIdPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();

  return (
    <>
      {/* <TitleHeader title="Menu Profile" type="fixed" /> */}
      <SearchMerchantID configs={configs} session={session as Session} />
    </>
  );
}
