import { unstable_noStore as noStore, revalidatePath } from "next/cache";
import { ICheckOrderSchema, ILoginSchema } from "./types";
import axios from "axios";
import { configs } from "./data";

export async function checkOrder(data: ICheckOrderSchema) {
  const api = new URL(`${process.env.BASE_URL}/api/check-order`);

  const { endPoint, url } = data;
  const qrUrl = url.split("qr?");
  const checkUrl = qrUrl[1];

  if (endPoint) {
    api.searchParams.append("endPoint", endPoint);
  }

  if (checkUrl) {
    api.searchParams.append("checkUrl", checkUrl);
  }

  const response = await axios.get(api.toString());
  return response;
}

export async function getData(endPoint: string, apiPath: string, id?: string) {
  const api = new URL(`${process.env.BASE_URL}/api/data`);

  let apiUrl: string;
  if (id) {
    apiUrl = `${endPoint}/api/${apiPath}/${id}`;
  } else {
    apiUrl = `${endPoint}/api/${apiPath}`;
  }

  if (endPoint) {
    api.searchParams.append("endPoint", endPoint);
  }

  if (apiUrl) {
    api.searchParams.append("apiUrl", apiUrl);
  }

  const response = await axios.get(api.toString());
  return response;
}

export async function deleteData(
  endPoint: string,
  apiPath: string,
  id?: string
) {
  const api = new URL(`${process.env.BASE_URL}/api/data`);

  let apiUrl: string;
  if (id) {
    apiUrl = `${endPoint}/api/${apiPath}/${id}`;
  } else {
    apiUrl = `${endPoint}/api/${apiPath}`;
  }

  if (endPoint) {
    api.searchParams.append("endPoint", endPoint);
  }

  if (apiUrl) {
    api.searchParams.append("apiUrl", apiUrl);
  }

  const response = await axios.delete(api.toString());
  return response;
}

export async function getConfig() {
  noStore();
  // const api = `${process.env.BASE_URL}/api/config`;

  // const response = await fetch(api);
  // return response.json();

  return configs;
}

export async function getDbData(
  apiPath: string,
  host?: string,
  username?: string
) {
  const api = new URL(`${process.env.BASE_URL}/api/${apiPath}`);

  if (host) {
    api.searchParams.append("host", host);
  }

  if (username) {
    api.searchParams.append("username", username);
  }

  const response = await axios.get(api.toString());
  return response;
}

export async function createData(apiPath: string, data: any) {
  try {
    const response = await axios.post(
      `${process.env.BASE_URL}/api/${apiPath}`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response;
  } catch (error) {
    console.error("Error while sending data:", error);
    throw error;
  }
}
