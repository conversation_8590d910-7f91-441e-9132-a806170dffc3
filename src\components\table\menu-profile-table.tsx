"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shadcn-ui/table";
import { Card, CardContent } from "@/components/shadcn-ui/card";
import React, { useState } from "react";
import { TableRowIdle, TableRowSkeleton } from "./table-row";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import { Trash } from "lucide-react";
import { Switch } from "@/components/shadcn-ui/switch";
import AlertModal from "../ui/alert-modal";
import axios from "axios";
import { endPointToken } from "@/lib/utils";
import { AlertBar, AlertProps } from "../ui/alert-bar";
import { ISearchMerchantIdSchema } from "@/lib/types";
import { Button } from "@/components/shadcn-ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/shadcn-ui/dropdown-menu";
import { admigLog } from "@/lib/actions";

type Props = {
  isLoading: boolean;
  data: any;
  endpoint: string;
  onSearch: (data: ISearchMerchantIdSchema) => void;
  merchantId: string;
  session: any;
};

export default function MenuProfileTable({
  isLoading,
  data,
  endpoint,
  onSearch,
  merchantId,
  session,
}: Props) {
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [isLoadingModal, setIsLoadingModal] = useState(false);
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const [isLoadingTable, setIsLoadingTable] = useState(false);
  const [isLoadingFeatureType, setIsLoadingFeatureType] = useState(false);
  const [selectedMenuProfileId, setSelectedMenuProfileId] = useState("");
  const clearAlert = () => setShowAlert(null);

  const handleToggle = (menuProfileId: string) => {
    setSelectedMenuProfileId(menuProfileId);
    setIsAlertOpen(true);
  };

  const handleCloseAlert = () => {
    setIsAlertOpen(false);
  };

  const toggleSelf = async (
    menuProfileId: number,
    merchantId: string,
    visibleSelfOrdering: boolean
  ) => {
    setIsLoadingTable(true);

    const params = {
      menuProfileId,
      merchantId,
      visibleSelfOrdering,
      language: "",
    };

    let url = `MenuProfile/SetPatchSelfOrdering`;
    try {
      const response = await axios.patch(`${endpoint}/api/${url}`, null, {
        params,
        headers: {
          Accept: "application/json",
          key: endPointToken(endpoint),
        },
      });
      if (response.status === 200) {
        setIsLoadingTable(false);
        setShowAlert({
          type: "success",
          detail: "Data saved",
          onClose: clearAlert,
        });
        setTimeout(() => {
          onSearch({
            endPoint: endpoint,
            merchantId: merchantId,
          });
        }, 0);
      } else {
        setIsLoadingTable(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        endpoint,
        session?.user?.name || "Anonymous",
        "PATCH",
        `${endpoint}/api/${url}`,
        JSON.stringify(params),
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setIsLoadingTable(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
    setIsAlertOpen(false);
  };

  const toggleWeb = async (
    menuProfileId: number,
    merchantId: string,
    visibleWebOrdering: boolean
  ) => {
    setIsLoadingTable(true);

    const params = {
      menuProfileId,
      merchantId,
      visibleWebOrdering,
      language: "",
    };

    let url = `MenuProfile/SetPatchWebOrdering`;
    try {
      const response = await axios.patch(`${endpoint}/api/${url}`, null, {
        params,
        headers: {
          Accept: "application/json",
          key: endPointToken(endpoint),
        },
      });
      if (response.status === 200) {
        setIsLoadingTable(false);
        setShowAlert({
          type: "success",
          detail: "Data saved",
          onClose: clearAlert,
        });
        setTimeout(() => {
          onSearch({
            endPoint: endpoint,
            merchantId: merchantId,
          });
        }, 0);
      } else {
        setIsLoadingTable(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        endpoint,
        session?.user?.name || "Anonymous",
        "PATCH",
        `${endpoint}/api/${url}`,
        JSON.stringify(params),
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setIsLoadingTable(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
    setIsAlertOpen(false);
  };

  const handleConfirm = async () => {
    const searchData: ISearchMerchantIdSchema = {
      endPoint: endpoint,
      merchantId: merchantId,
    };
    setIsLoadingModal(true);
    let url = `ClearMenuData?merchantId=${merchantId}&menuProfileId=${selectedMenuProfileId}`;
    try {
      // const response = await deleteData(getValues("endPoint"), url);
      const response = await axios.delete(`${endpoint}/api/${url}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endpoint),
        },
      });
      if (response.status === 200) {
        setIsLoadingModal(false);
        onSearch(searchData);
      } else {
        setIsLoadingModal(false);
        onSearch(searchData);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        endpoint,
        session?.user?.name || "Anonymous",
        "DELETE",
        `${endpoint}/api/${url}`,
        "",
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setIsLoadingModal(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
      onSearch(searchData);
    }
    setIsAlertOpen(false);
  };

  const changeFeatureType = async (
    merchantId: string,
    menuProfileId: number,
    featureType: string
  ) => {
    setIsLoadingFeatureType(true);

    const params = {
      merchantId,
      menuProfileId,
      featureType,
      language: "",
    };

    let url = `MenuProfile/SetPatchFeatureType`;
    try {
      const response = await axios.patch(`${endpoint}/api/${url}`, null, {
        params,
        headers: {
          Accept: "application/json",
          key: endPointToken(endpoint),
        },
      });
      if (response.status === 200) {
        setIsLoadingFeatureType(false);
        setShowAlert({
          type: "success",
          detail: "Data saved",
          onClose: clearAlert,
        });
        setTimeout(() => {
          onSearch({
            endPoint: endpoint,
            merchantId: merchantId,
          });
        }, 0);
      } else {
        setIsLoadingFeatureType(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        endpoint,
        session?.user?.name || "Anonymous",
        "PATCH",
        `${endpoint}/api/${url}`,
        JSON.stringify(params),
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setIsLoadingFeatureType(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
    setIsAlertOpen(false);
  };

  return (
    <Card>
      <CardContent>
        <ScrollArea className="mt-6 border whitespace-nowrap rounded-md">
          <Table>
            <TableHeader className="bg-primary">
              <TableRow>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center rounded-tl-md w-[10%]">
                  No.
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                  Code
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[25%] truncate">
                  Name
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                  Visible Self
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                  Visible Web
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%] truncate">
                  Feature Type
                </TableHead>
                <TableHead className="rounded-tr-md w-[5%]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data ? (
                <>
                  {data.map((item: any, i: number) => (
                    <React.Fragment key={i}>
                      <TableRow>
                        <TableCell className="font-medium text-center">
                          {i + 1}
                        </TableCell>
                        <TableCell className="text-center">
                          {item?.menuProfileCode || " "}
                        </TableCell>
                        <TableCell className="text-center">
                          {item?.menuProfileName || " "}
                        </TableCell>
                        <TableCell className="text-center">
                          <Switch
                            disabled={isLoadingTable}
                            id="visible-self-ordering"
                            checked={!!item?.visibleSelfOrdering}
                            onCheckedChange={(checked) =>
                              toggleSelf(
                                item?.menuProfileId,
                                merchantId,
                                checked
                              )
                            }
                          />
                        </TableCell>
                        <TableCell className="text-center">
                          <Switch
                            disabled={isLoadingTable}
                            id="visible-web-ordering"
                            checked={item?.visibleWebOrdering}
                            onCheckedChange={(checked) =>
                              toggleWeb(
                                item?.menuProfileId,
                                merchantId,
                                checked
                              )
                            }
                          />
                        </TableCell>
                        <TableCell className="text-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                disabled={isLoadingFeatureType}
                              >
                                {item?.featureType.toString() || " "}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuLabel>
                                Feature Type
                              </DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuRadioGroup
                                value={item?.featureType.toString()}
                                onValueChange={(value) =>
                                  changeFeatureType(
                                    merchantId,
                                    item?.menuProfileId,
                                    value
                                  )
                                }
                              >
                                <DropdownMenuRadioItem value="0">
                                  0
                                </DropdownMenuRadioItem>
                                <DropdownMenuRadioItem value="1">
                                  1
                                </DropdownMenuRadioItem>
                              </DropdownMenuRadioGroup>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            type="button"
                            size={"icon"}
                            variant={"ghost"}
                            onClick={() => handleToggle(item?.menuProfileId)}
                          >
                            <Trash className="size-5 text-primary font-semibold text-center" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    </React.Fragment>
                  ))}
                </>
              ) : isLoading ? (
                <TableRowSkeleton numCol={6} />
              ) : (
                <TableRowIdle title="Search to see data." colSpan={6} />
              )}
            </TableBody>
            <AlertModal
              title="Are you absolutely sure?"
              desc="This action cannot be undone. This will permanently remove data from our servers."
              type="danger"
              open={isAlertOpen}
              isVerify={false}
              isLoading={isLoadingModal}
              onClose={handleCloseAlert}
              onSubmit={handleConfirm}
            />
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </CardContent>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </Card>
  );
}
