"use client";
import Image from "next/image";
import Link from "next/link";
import { Label } from "@/components/shadcn-ui/label";
import { Input } from "@/components/shadcn-ui/input";
import { Button } from "@/components/shadcn-ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ILoginSchema, loginSchema } from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { endPontList } from "@/lib/data";
import { useRouter } from "next/navigation";
import { Loader2, LogIn } from "lucide-react";
import { PasswordInput } from "@/components/shadcn-ui/password-input";
import { signIn } from "next-auth/react";
import { login } from "@/lib/actions";

export default function LoginPage() {
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<ILoginSchema>({
    resolver: zodResolver(loginSchema),
  });


  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);
  const router = useRouter();

  const validateFormData = (data: ILoginSchema): boolean => {
    let isValid = true;

    // if (!data.endPoint) {
    //   setError("endPoint", {
    //     type: "server",
    //     message: "Please select Project.",
    //   });
    //   isValid = false;
    // }

    if (!data.username) {
      setError("username", {
        type: "server",
        message: "Please enter your username.",
      });
      isValid = false;
    }

    if (!data.password) {
      setError("password", {
        type: "server",
        message: "Please enter your password.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: ILoginSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    try {
      const response = await login(data);
      if (response.isCorrect) {
        const signInData = await signIn('credentials', {
          username: data.username,
          password: data.password,
          redirect: false
        });
        router.push('/dashboard');
        
        if (signInData?.error) {
          console.log(signInData.error);
        } else {
          setShowAlert({
            type: "success",
            detail: "Login Successfully",
            onClose: clearAlert,
          });
        }
      } else {
        setShowAlert({
          type: "warning",
          detail: "Login failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
    } catch {
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  return (
    <div className="w-full lg:grid lg:grid-cols-2 min-h-screen">
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold">Login</h1>
            <p className="text-balance text-muted-foreground">
              Infogrammer Authentication 🔒
            </p>
          </div>
          <form
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="grid gap-4">
              {/* <div className="grid gap-2">
                <div className="flex w-full flex-col space-y-2">
                  <Label htmlFor="end-point">End Point</Label>
                  <Select
                    name="endPoint"
                    onValueChange={(value) => setValue("endPoint", value)}
                  >
                    <SelectTrigger id="end-point" aria-label="Select project">
                      <SelectValue placeholder="-" />
                    </SelectTrigger>
                    <SelectContent>
                      {endPontList.map((item) => (
                        <SelectItem
                          key={item.id}
                          value={`${item.url}/${item.apiLoginPath}`}
                        >
                          {item.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.endPoint && (
                    <p className="text-sm text-red-500">
                      {errors.endPoint.message}
                    </p>
                  )}
                </div>
              </div>  */}
              <div className="grid gap-2">
                <div className="flex w-full flex-col space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    {...register("username")}
                    id="username"
                    name="username"
                    type="username"
                    placeholder="infogrammer"
                  />
                  {errors.username && (
                    <p className="text-sm text-red-500">
                      {errors.username.message}
                    </p>
                  )}
                </div>
              </div>
              <div className="grid gap-2">
                <div className="flex w-full flex-col space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="password">Password</Label>
                    {/* <Link
                      href="/forgot-password"
                      className="ml-auto inline-block text-sm underline"
                    >
                      Forgot your password?
                    </Link> */}
                  </div>
                  <PasswordInput
                    {...register("password")}
                    id="password"
                    name="password"
                    placeholder="●●●●●●●●●●●●●"
                  />
                  {errors.password && (
                    <p className="text-sm text-red-500">
                      {errors.password.message}
                    </p>
                  )}
                </div>
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                <span className="flex items-center justify-center gap-1">
                  {isSubmitting ? (
                    <>
                      <p>Loging...</p>
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </>
                  ) : (
                    <>
                      <p>Login</p>
                      <LogIn />
                    </>
                  )}
                </span>
              </Button>
            </div>
          </form>
          {/* <div className="mt-4 text-center text-sm">
            Don&apos;t have an account?{" "}
            <Link href="#" className="underline">
              Sign up
            </Link>
          </div> */}
        </div>
      </div>
      <div className="hidden bg-muted lg:block">
        <Image
          src="/assets/mr.thong-cat.jpg"
          alt="Image"
          width="1920"
          height="1080"
          className="h-screen w-full object-cover dark:brightness-[0.5]"
          loading="lazy"
        />
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
