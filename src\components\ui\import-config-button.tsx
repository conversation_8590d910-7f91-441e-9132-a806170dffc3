import React, { ChangeEvent, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/shadcn-ui/button";
import { CircleHelp, FileUp } from "lucide-react";
import { parseEnvFile, parseJsonFile } from "@/lib/utils";
import TooltipHover from "./tooltip-hover";

interface Props {
  appendEnvOptions: (options: { key: string; value: string }[]) => void;
}

export default function ImportConfigButton({ appendEnvOptions }: Props) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleImportFile = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target?.result) {
            const text = event.target.result as string;
            if (
              file.name.endsWith(".env") ||
              file.name.endsWith(".env.local") ||
              file.name.endsWith(".txt")
            ) {
              const parsedData = parseEnvFile(text);
              appendEnvOptions(parsedData);
            } else if (file.name.endsWith(".json")) {
              const parsedData = parseJsonFile(text);
              appendEnvOptions(parsedData);
            } else {
              console.error("Unsupported file format");
            }
          }
        };
        reader.readAsText(file);
      }
    },
    [appendEnvOptions]
  );

  return (
    <div className="flex gap-2 items-center">
      <Button
        variant="outline"
        className="mt-0 flex gap-1 w-fit"
        onClick={handleButtonClick}
      >
        <FileUp className="w-4 h-4" />
        <span>Import&nbsp;Config</span>
        <input
          ref={fileInputRef}
          type="file"
          accept=".env, .env.local, .json, .txt"
          onChange={handleImportFile}
          className="hidden"
        />
      </Button>
      <TooltipHover
        position="right"
        content={<span className="text-sm">File (.env/.json/.txt)</span>}
      >
        <CircleHelp className="w-4 h-4 opacity-70 hover:opacity-100" />
      </TooltipHover>
    </div>
  );
}
