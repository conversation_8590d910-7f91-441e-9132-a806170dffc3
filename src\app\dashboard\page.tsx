import { authOptions } from "@/auth";
import { WarpBackground } from "@/components/shadcn-ui/warp-background";
import HyperText from "@/components/ui/hyper-text";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Home",
};

export default async function HomePage() {
  const session = await getServerSession(authOptions);

  return (
    <WarpBackground className="flex flex-1 items-center justify-center rounded-lg h-dvh bg-sidebar/50">
      {/* <div className="flex flex-1 items-center justify-center rounded-lg h-full"> */}
        <div className="flex flex-col items-center gap-1 text-center bg-sidebar/70 border p-4 rounded-lg">
          <HyperText
            className="text-4xl cursor-default font-bold tracking-tight uppercase text-primary"
            text={session?.user.name as string}
            capitalLetter="uppercase"
          />
          <HyperText
            className="text-3xl cursor-default font-semibold tracking-tight"
            text="Super Admin"
            capitalLetter="lowercase"
          />
          <HyperText
            className="text-lg cursor-default text-muted-foreground"
            text="Infogrammer Co., Ltd."
            capitalLetter="lowercase"
          />
        </div>
      {/* </div> */}
    </WarpBackground>
  );
}
