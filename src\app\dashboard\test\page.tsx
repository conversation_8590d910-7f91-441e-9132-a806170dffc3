// "use client"
import { Decrypt, Encrypt } from "@/lib/encode";
import React from "react";
import { format } from "date-fns-tz";

const timeZone = "Asia/Bangkok";
const currentDateTime = format(new Date(), "yyyy-MM-dd'T'HH:mm:sss.SSSXXX", {
  timeZone,
});

export default async function page() {
  // const data = {
  //   MQTTDateTime: "2024-10-28T14:17:10.9758871+07:00",
  //   ViewDetail: null,
  //   MQTTMessage: null
  // };

  // const text = "BcZNBuZxRO5BH9J4uzfFDNV6YL+cUlat21WLdIzgkBQ=";

  // const textEncrypt = Encrypt(JSON.stringify(data));
  // const textDecrypt = Decrypt(textEncrypt);

  // const folders = await getFTPFolder();

  return (
    <div>
      <h2>Folders</h2>
      {/* <ul>
        {folders?.map((folder: any, index: number) => (
          <li key={index}>{folder}</li>
        ))}
      </ul> */}
    </div>
  );
}
