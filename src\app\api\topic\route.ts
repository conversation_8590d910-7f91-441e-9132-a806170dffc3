import { authOptions } from "@/auth";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth/next";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  const hostUrl = req.nextUrl.searchParams.get("host");
  const username = req.nextUrl.searchParams.get("username");

  if (!hostUrl) {
    return NextResponse.json(
      { error: "Missing host URL parameter" },
      { status: 400 }
    );
  }

  try {
    const result = await prisma.$transaction(async (prisma) => {
      // Find or create Host
      let host = await prisma.host.findUnique({
        where: { url: hostUrl },
      });

      if (!host) {
        host = await prisma.host.create({
          data: {
            url: hostUrl,
            username: username || "", // Default to empty string if username is not provided
          },
        });
      }

      // Fetch topics associated with the host
      const topics = await prisma.topic.findMany({
        where: { hostId: host.id },
        include: { messages: true },
      });

      return { topics, host }; // Return topics to be sent as response
    });

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Error fetching data:", error);
    return NextResponse.json(
      { error: "Failed to fetch data" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  const data = await req.json();
  // console.log("🚀 ~ POST ~ data:", data);
  const { hostUrl, payload } = data;

  const results = [];

  for (const item of payload) {
    const { topic, message } = item;

    if (!topic || !message) {
      return NextResponse.json(
        { error: "Topic and message are required" },
        { status: 400 }
      );
    }

    try {
      let host = await prisma.host.findUnique({
        where: { url: hostUrl },
      });

      if (!host) {
        return NextResponse.json(
          { error: "Host not found or created" },
          { status: 404 }
        );
      }

      let topicRecord = await prisma.topic.findUnique({
        where: { name: topic },
      });

      if (!topicRecord) {
        topicRecord = await prisma.topic.create({
          data: {
            name: topic,
            host: { connect: { id: host.id } },
          },
        });
      }

      // const existingMessage = await prisma.message.findUnique({
      //   where: { identifier },
      // });

      // if (existingMessage) {
      //   continue; // Skip duplicate message
      // }

      const newMessage = await prisma.message.create({
        data: {
          content: message,
          topicId: topicRecord.id,
        },
      });

      results.push(newMessage);
    } catch (error) {
      console.error("Error fetching data:", error);
      return NextResponse.json(
        { error: "Failed to fetch data" },
        { status: 500 }
      );
    }
  }

  return NextResponse.json(results, { status: 200 });
}
