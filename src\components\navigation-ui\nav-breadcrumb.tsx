"use client";
import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "../shadcn-ui/breadcrumb";
import { usePathname } from "next/navigation";
import { INavMenu, ISubMenu, navMenu } from "@/lib/data";

export default function NavBreadcrumb() {
  const pathname = usePathname();

  const findLabelFromPath = (
    path: string,
    menu: INavMenu[] | ISubMenu[]
  ): string | null => {
    for (const item of menu) {
      if (item.href === path) {
        return item.name;
      }
      if ("sub_menu" in item && item.sub_menu.length > 0) {
        const subLabel = findLabelFromPath(path, item.sub_menu);
        if (subLabel) return subLabel;
      }
    }
    return null;
  };

  const pathSegments = pathname.split("/").filter(Boolean);
  const breadcrumbItems = pathSegments.map((segment, index) => {
    const href = `/${pathSegments.slice(0, index + 1).join("/")}`;

    const labelFromNavMenu = findLabelFromPath(href, navMenu);

    const label = labelFromNavMenu
      ? labelFromNavMenu
      : segment
          .replace(/-/g, " ")
          .split(" ")
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(" ");

    return { href, label };
  });

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.length > 1
          ? breadcrumbItems.map((item, index) => (
              <React.Fragment key={item.href}>
                {index > 0 && (
                  <>
                    {index > 1 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                    <BreadcrumbItem className="hidden md:block">
                      <BreadcrumbLink
                        href={index === 0 ? item.href : undefined}
                      >
                        {item.label}
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </>
                )}
                {index === breadcrumbItems.length - 1 && (
                  <BreadcrumbItem className="block md:hidden">
                    <BreadcrumbLink>{item.label}</BreadcrumbLink>
                  </BreadcrumbItem>
                )}
              </React.Fragment>
            ))
          : breadcrumbItems.map((item) => (
              <BreadcrumbItem key={item.href}>
                <BreadcrumbLink href={item.href}>{item.label}</BreadcrumbLink>
              </BreadcrumbItem>
            ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
