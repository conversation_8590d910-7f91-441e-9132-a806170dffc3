"use client";
import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { end<PERSON>ontList, languageList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ISettingConfigSchema, settingConfigSchema } from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useEffect, useState } from "react";
import Combobox from "@/components/ui/combobox";
import { CircleMinus, CirclePlus, Loader2 } from "lucide-react";
import FixedContent from "@/components/ui/fixed-content";
import { Session } from "next-auth";
import { cn } from "@/lib/utils";
import { getData, getDataById, updateConfigPOS, admigLog } from "@/lib/actions";
import { Input } from "@/components/shadcn-ui/input";
import ImportConfigButton from "@/components/ui/import-config-button";

type Props = {
  session: Session;
  configs: any;
};

export default function SettingConfig({ session, configs }: Props) {
  const isFixProject = configs.isFixProject;

  const {
    control,
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
    clearErrors,
  } = useForm<ISettingConfigSchema>({
    resolver: zodResolver(settingConfigSchema),
    defaultValues: {
      endPoint: isFixProject ? session.user.endPoint : "",
      envOptions: [{ key: "", value: "" }],
    },
  });

  const [merchant, setMerchant] = useState<any>();
  const [merchantGroup, setMerchantGroup] = useState<any>();
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);
  const [merchantLoading, setMerchantLoading] = useState<boolean>(false);
  const [merchantGroupLoading, setMerchantGroupLoading] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { fields, append, remove } = useFieldArray({
    control,
    name: "envOptions",
  });

  const renderSelectProject = () => {
    switch (isFixProject) {
      case true:
        return (
          <Controller
            name="endPoint"
            control={control}
            render={({ field }) => {
              const selectedItem = endPontList.find(
                (item) => item.url === field.value
              );
              return (
                <Select
                  onValueChange={async (value) => {
                    field.onChange(value);
                    setValue("endPoint", value);
                    setValue("merchantGroupId", "");
                    setValue("merchantId", []);
                    setMerchant(null);
                    setMerchantGroup(null);

                    clearErrors("endPoint");

                    await findMerchantGroup(value, "merchantgroup");
                    await findMerchant(value, "merchant/filter");
                  }}
                  value={field.value}
                  disabled={isFixProject}
                >
                  <SelectTrigger id="project" aria-label="Select project">
                    <SelectValue placeholder="Select project">
                      {selectedItem ? selectedItem.name : "Select Project"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {endPontList.map((item) => (
                      <SelectItem key={item.id} value={item.url}>
                        {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            }}
          />
        );

      case false:
        return (
          <Select
            value={getValues("endPoint")}
            onValueChange={async (value) => {
              setValue("endPoint", value);
              setValue("merchantGroupId", "");
              setValue("merchantId", []);
              setMerchant(null);
              setMerchantGroup(null);

              clearErrors("endPoint");

              await findMerchantGroup(value, "merchantgroup");
              await findMerchant(value, "merchant/filter");
            }}
          >
            <SelectTrigger id="project" aria-label="Select project">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {endPontList.map((item) => (
                <SelectItem key={item.id} value={item.url}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
    }
  };

  const findMerchantGroup = async (endPoint: string, apiPath: string) => {
    setMerchantGroupLoading(true);
    setMerchantGroup(null);
    try {
      const response = await getData(endPoint, apiPath);
      if (response.status === 200) {
        setMerchantGroup(response.data);
        setMerchantGroupLoading(false);
      } else {
        setMerchantGroupLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
    } catch (error) {
      setMerchantGroupLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, can not find merchant group id",
        onClose: clearAlert,
      });
    }
  };

  const findMerchant = async (
    endPoint: string,
    apiPath: string,
    id?: string
  ) => {
    setMerchantLoading(true);
    setMerchant(null);

    if (id) {
      try {
        const response = await getDataById(endPoint, apiPath, id);
        if (response.status === 200) {
          setMerchant(response.data);
          setMerchantLoading(false);
        } else {
          setMerchantLoading(false);
          setShowAlert({
            type: "warning",
            detail: "Search failed, please try again.",
            onClose: clearAlert,
          });
          return;
        }
      } catch (error) {
        setMerchantLoading(false);
        setShowAlert({
          type: "error",
          detail: "Something went wrong, can not find merchant id",
          onClose: clearAlert,
        });
      }
    } else {
      try {
        const response = await getData(endPoint, apiPath);
        if (response.status === 200) {
          setMerchant(response.data);
          setMerchantLoading(false);
        } else {
          setMerchantLoading(false);
          setShowAlert({
            type: "warning",
            detail: "Search failed, please try again.",
            onClose: clearAlert,
          });
          return;
        }
      } catch (error) {
        setMerchantLoading(false);
        setShowAlert({
          type: "error",
          detail: "Something went wrong, can not find merchant id",
          onClose: clearAlert,
        });
      }
    }
  };

  useEffect(() => {
    if (isFixProject) {
      const endPoint = getValues("endPoint");
      if (endPoint) {
        findMerchantGroup(endPoint, "merchantgroup");
        findMerchant(endPoint, "merchant/filter");
      }
    }
  }, [isFixProject, getValues]);

  const validateFormData = (data: ISettingConfigSchema): boolean => {
    let isValid = true;

    if (!data.endPoint) {
      setError("endPoint", {
        type: "server",
        message: "Please select Project.",
      });
      isValid = false;
    }

    if (!data.merchantId) {
      setError("merchantId", {
        type: "server",
        message: "Please select Merchant ID.",
      });
      isValid = false;
    }

    data.envOptions.forEach((option, index) => {
      if (!option.key) {
        setError(`envOptions.${index}.key` as any, {
          type: "server",
          message: `Please enter Key ${index + 1}.`,
        });
        isValid = false;
      }

      if (!option.value) {
        setError(`envOptions.${index}.value` as any, {
          type: "server",
          message: `Please enter Value ${index + 1}.`,
        });
        isValid = false;
      }
    });

    return isValid;
  };

  const formatFormData = (data: ISettingConfigSchema) => {
    const formattedOptions = data.envOptions.reduce((acc, option) => {
      (acc as { [key: string]: string })[option.key] = option.value;
      return acc;
    }, {});

    const isMultipleMerchantId = Array.isArray(data.merchantId);
    let formattedMerchantId;

    if (isMultipleMerchantId) {
      const merchantIdArray = data.merchantId.map((id) => Number(id));
      formattedMerchantId =
        merchantIdArray.length === 1 ? merchantIdArray[0] : merchantIdArray;
    } else {
      formattedMerchantId = Number(data.merchantId);
    }

    const formatData = {
      MerchantGroupId: data.merchantGroupId ? Number(data.merchantGroupId) : 0,
      MerchantId: formattedMerchantId,
      Options: formattedOptions,
      IsUpdateHQ: Number(data.merchantGroupId) > 0 && true,
    };

    return formatData;
  };

  const onSubmit = async (data: ISettingConfigSchema) => {
    if (!validateFormData(data)) {
      return;
    }
    setLoading(true);
    let responseStatus: string = "";
    try {
      const response = await updateConfigPOS(
        data.endPoint,
        formatFormData(data)
      );
      if (Array.isArray(response)) {
        const allSuccess = response.every((res) => res.status === 200);
        if (allSuccess) {
          onSuccess();
          onReset();
          responseStatus = "200";
        } else {
          setLoading(false);
          setShowAlert({
            type: "warning",
            detail: "Setting failed for some merchants, please try again.",
            onClose: clearAlert,
          });
        }
      } else {
        if (response.status === 200) {
          onSuccess();
          onReset();
          responseStatus = "200";
        } else {
          setLoading(false);
          responseStatus = "500";
          setShowAlert({
            type: "warning",
            detail: "Setting failed, please try again.",
            onClose: clearAlert,
          });
        }
      }
      await admigLog(
        data.endPoint,
        session?.user?.name || "Anonymous",
        "POST",
        `${data.endPoint}/api/config/pos`,
        JSON.stringify(formatFormData(data)),
        JSON.stringify(response),
        responseStatus
      );
    } catch {
      setLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  const onSuccess = () => {
    setLoading(false);
    setShowAlert({
      type: "success",
      detail: "Setting config success.",
      onClose: clearAlert,
    });
  };

  const onReset = () => {
    setMerchantGroup(null);
    setMerchant(null);
    setValue("endPoint", isFixProject ? session.user.endPoint : "");
    setValue("merchantGroupId", "");
    setValue("merchantId", []);
    setValue("envOptions", [{ key: "", value: "" }]);
    reset();
  };

  const appendEnvOptions = (parsedData: ISettingConfigSchema["envOptions"]) => {
    parsedData.forEach((item) => append(item));
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="grid gap-4 grid-cols-12 w-full relative">
        <FixedContent
          parentClassName="col-span-12 lg:col-span-6 xl:col-span-4"
          childClassName="lg:fixed lg:z-10 lg:w-full"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-xl md:text-2xl">POS Config</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="project">Project</Label>
                  {renderSelectProject()}
                  {errors.endPoint && (
                    <p className="text-sm text-red-500">
                      {errors.endPoint.message}
                    </p>
                  )}
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="merchantId">
                    Merchant Group ID&nbsp;
                    <span className="text-[12px] opacity-70">(Optinal)</span>
                  </Label>
                  <Combobox
                    title="Merchant Group ID"
                    listData={merchantGroup}
                    valueKey="merchantGroupId"
                    nameKey="merchantGroupName"
                    loading={merchantGroupLoading}
                    showValueWithName={true}
                    showResetButton={true}
                    onValueChange={(selectedValue) => {
                      setValue("merchantGroupId", selectedValue),
                        setValue("merchantId", []),
                        findMerchant(
                          getValues("endPoint"),
                          "merchant/MerchantGroup",
                          selectedValue
                        ),
                        clearErrors("merchantGroupId");
                    }}
                    onValueReset={() => {
                      setValue("merchantGroupId", ""),
                        findMerchant(getValues("endPoint"), "merchant/filter");
                    }}
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="merchantId">Merchant ID</Label>
                  <Combobox
                    title="Merchant ID"
                    listData={merchant}
                    valueKey="merchantId"
                    nameKey="merchantName"
                    slugKey="slug"
                    loading={merchantLoading}
                    showValueWithName={true}
                    isMultiple={true}
                    showResetButton={true}
                    onValueChange={(selectedValue) => {
                      setValue("merchantId", selectedValue),
                        clearErrors("merchantId");
                    }}
                    onValueReset={() => {
                      setValue("merchantId", []);
                    }}
                  />
                  {merchant && errors.merchantId && (
                    <p className="text-sm text-red-500">
                      {errors.merchantId.message}
                    </p>
                  )}
                  {merchant?.length === 0 && (
                    <p className="text-sm text-red-500">No Merchant ID!</p>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="gap-2 justify-center">
              {/* <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full"
                >
                  <span className="flex items-center justify-center gap-2">
                    {isSubmitting ? (
                      <>
                        <p>Searching...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Search</p>
                      </>
                    )}
                  </span>
                </Button> */}
            </CardFooter>
          </Card>
        </FixedContent>
        <div className="col-span-12 lg:col-span-6 xl:col-span-8 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl md:text-2xl">Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2">
                {fields.map((field, index) => (
                  <div
                    key={field.id}
                    className="grid gap-2 grid-cols-[1fr_1fr_auto] items-center"
                  >
                    <div className="grid gap-3">
                      {index === 0 && (
                        <Label htmlFor={`envOptions.${index}.key`}>Key</Label>
                      )}
                      <Input
                        {...register(`envOptions.${index}.key`)}
                        id={`envOptions.${index}.key`}
                        name={`envOptions.${index}.key`}
                        type="text"
                        placeholder=""
                        onChange={() => {
                          clearErrors(`envOptions.${index}.key`);
                        }}
                      />
                      {errors.envOptions?.[index]?.key && (
                        <p className="text-sm text-red-500">
                          {errors.envOptions[index].key.message}
                        </p>
                      )}
                      {errors.envOptions?.[index]?.value && (
                        <div className="mt-5" />
                      )}
                    </div>
                    <div className="grid gap-3">
                      {index === 0 && (
                        <Label htmlFor={`envOptions.${index}.value`}>
                          Value
                        </Label>
                      )}
                      <Input
                        {...register(`envOptions.${index}.value`)}
                        id={`envOptions.${index}.value`}
                        name={`envOptions.${index}.value`}
                        type="text"
                        placeholder=""
                        onChange={() => {
                          clearErrors(`envOptions.${index}.value`);
                        }}
                      />
                      {errors.envOptions?.[index]?.value && (
                        <p className="text-sm text-red-500">
                          {errors.envOptions[index].value.message}
                        </p>
                      )}
                      {errors.envOptions?.[index]?.key && (
                        <div className="mt-5" />
                      )}
                    </div>
                    <div className="grid gap-3 items-start">
                      <Button
                        variant="ghost"
                        size="icon"
                        className={cn(
                          index === 0 &&
                            !(
                              errors.envOptions?.[index]?.key ||
                              errors.envOptions?.[index]?.value
                            )
                            ? "mt-6"
                            : "mt-0",
                          index !== 0 &&
                            (errors.envOptions?.[index]?.key ||
                              errors.envOptions?.[index]?.value)
                            ? "-translate-y-8"
                            : "-translate-y-5",
                          !(
                            errors.envOptions?.[index]?.key ||
                            errors.envOptions?.[index]?.value
                          ) && "translate-y-0"
                        )}
                        onClick={() => remove(index)}
                        disabled={fields.length === 1}
                      >
                        <CircleMinus className="w-5 h-5" />
                        <span className="sr-only">Remove row</span>
                      </Button>
                    </div>
                  </div>
                ))}
                {errors.envOptions && (
                  <p className="text-sm text-red-500">
                    {errors.envOptions.message}
                  </p>
                )}
                <Button
                  variant="outline"
                  className="mt-0 flex gap-1 w-fit"
                  onClick={() => append({ key: "", value: "" })}
                >
                  <CirclePlus className="w-4 h-4" />
                  Add Row
                </Button>
              </div>
            </CardContent>
            <hr className="mb-6 -mt-2 h-[1.25px] w-full bg-primary-foreground/80 dark:bg-primary-foreground/20 rounded-sm" />
            <CardFooter className="flex justify-between items-center gap-2 -my-3">
              <ImportConfigButton appendEnvOptions={appendEnvOptions} />
              <Button
                onClick={() => onSubmit(getValues())}
                className="w-fit"
                disabled={loading}
              >
                <span className="flex items-center justify-center gap-2">
                  {loading ? (
                    <>
                      <p>Checking...</p>
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </>
                  ) : (
                    <>
                      <p>Save</p>
                    </>
                  )}
                </span>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
