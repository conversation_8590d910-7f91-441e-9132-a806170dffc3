{"name": "infogrammer-super-admin", "version": "3.0.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && node server.js", "build": "next build", "start": "set NODE_OPTIONS=--openssl-legacy-provider && next start", "lint": "next lint", "vercel-build": "prisma generate && prisma migrate deploy && next build"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@prisma/client": "^5.16.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.6", "axios": "^1.7.2", "basic-ftp": "^5.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "framer-motion": "^11.15.0", "lucide-react": "^0.426.0", "mqtt": "^5.7.2", "next": "^14.2.15", "next-auth": "4.24.7", "next-session": "^4.0.5", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.0", "sharp": "^0.33.4", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "xml2js": "^0.6.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/xml2js": "^0.4.14", "eslint": "^8", "eslint-config-next": "^14.2.7", "postcss": "^8", "prisma": "^5.16.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}