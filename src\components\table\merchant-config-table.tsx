"use client";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardContent,
} from "@/components/shadcn-ui/card";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/shadcn-ui/table";
import React, { useState } from "react";
import { TableRowIdle, TableRowSkeleton } from "./table-row";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import { Trash } from "lucide-react";
import { Switch } from "@/components/shadcn-ui/switch";
import AlertModal from "../ui/alert-modal";
import axios from "axios";
import { endPointToken } from "@/lib/utils";
import { AlertBar, AlertProps } from "../ui/alert-bar";
import { ISearchMerchantIdSchema } from "@/lib/types";
import { Button } from "@/components/shadcn-ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuRadioGroup,
    DropdownMenuRadioItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/shadcn-ui/dropdown-menu";
import { admigLog } from "@/lib/actions";

type Props = {
    isLoading: boolean;
    data: any;
    endpoint: string;
    onSearch: (data: ISearchMerchantIdSchema) => void;
    merchantId: string;
    session: any;
};

export default function MerchantConfigTable({
    isLoading,
    data,
    endpoint,
    onSearch,
    merchantId,
    session,
}: Props) {
    const [isAlertOpen, setIsAlertOpen] = useState(false);
    const [isLoadingModal, setIsLoadingModal] = useState(false);
    const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
    const [isLoadingTable, setIsLoadingTable] = useState(false);
    const [isLoadingFeatureType, setIsLoadingFeatureType] = useState(false);
    const clearAlert = () => setShowAlert(null);


    //* #################### Render ข้อมูล Configs ของ Merchant ####################
    const rendermerchantconfig = () => {
        if (!data?.configs) return null;

        return Object.entries(data.configs).map(([key, item], i) => {
            return (
                <React.Fragment key={i}>
                    <TableRow>
                        <TableCell className="font-medium text-center">
                            {i + 1}
                        </TableCell>
                        <TableCell className="text-left">
                            {(item as any)?.configKeyName || "—"}
                        </TableCell>
                        <TableCell className="text-left">
                            {(item as any)?.configValues || "—"}
                        </TableCell>
                    </TableRow>
                </React.Fragment>
            )
        });
    }
    //*###########################################################################

    return (
        <div className="grid grid-cols-12 gap-4">
            <Card className="col-span-12 lg:col-span-12">
                <CardHeader>
                    <CardTitle className="text-xl md:text-2xl">
                        ตั้งค่า Merchant
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <ScrollArea className="mt-6 border whitespace-nowrap rounded-md">
                        <Table>
                            <TableHeader className="bg-primary">
                                <TableRow>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center rounded-tl-md w-[10%]">
                                        No.
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[25%] truncate">
                                        Config Key Name
                                    </TableHead>
                                    <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[10%] truncate">
                                        Value
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {rendermerchantconfig()}
                            </TableBody>
                        </Table>
                        <ScrollBar orientation="horizontal" />
                    </ScrollArea>
                </CardContent>
            </Card>
        </div>
    );
}