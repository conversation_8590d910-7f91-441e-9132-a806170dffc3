import CheckOrder from "@/components/page-ui/order/check-order";
import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "Check Order",
};

export default async function CheckOrderPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();

  return (
    <>
      {/* <TitleHeader title="Order" type="fixed" /> */}
      <CheckOrder configs={configs} session={session as Session} />
    </>
  );
}
