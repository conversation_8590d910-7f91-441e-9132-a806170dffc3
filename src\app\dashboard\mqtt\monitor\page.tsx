import Monitor from "@/components/page-ui/mqtt/monitor-ui/monitor";
import TitleHeader from "@/components/ui/title-header";
import { Metadata } from "next/types";
import React from "react";

export const metadata: Metadata = {
  title: "MQTT - Monitor",
};

export default async function MqttMonitorPage() {
  return (
    <>
      {/* <TitleHeader title="MQTT - Monitor" type="static" screen="md" /> */}
      <Monitor />
    </>
  );
}
