"use client";
import React, { createContext, useEffect, useState } from "react";
import mqtt, { MqttClient } from "mqtt";
import { decodeJSONMessage } from "@/lib/utils";
import ResultStatus, { MessagePayload } from "./result-status";
import PublishCheck from "./publish-check";
import CheckStatusConnection from "./connection";

export const QosOption = createContext([]);
const qosOption = [
  {
    label: "0",
    value: 0,
  },
  {
    label: "1",
    value: 1,
  },
  {
    label: "2",
    value: 2,
  },
];

export default function CheckStatus() {
  const [client, setClient] = useState<MqttClient | null>(null);
  const [isPublishCheckSubed, setIsPublishCheckSub] = useState<boolean | null>(
    null
  );
  const [isConnect, setIsConnect] = useState<boolean | null>(null);
  const [connectStatus, setConnectStatus] = useState("Connect");
  const [payload, setPayload] = useState<MessagePayload | null>(null);
  const [subscriptionDetail, setSubscriptionDetail] = useState(null);
  const [isMerchantChanged, setMerchantChanged] = useState(false);

  const mqttConnect = (host: string, mqttOption: mqtt.IClientOptions) => {
    setConnectStatus("Connecting");
    const conn = mqtt.connect(host, mqttOption);
    setClient(conn);
  };

  useEffect(() => {
    if (client) {
      client.on("connect", async () => {
        setConnectStatus("Connected");
        setIsConnect(true);
        console.info("connection successful");
      });

      client.on("error", (err) => {
        console.error("Connection error: ", err);
        client.end();
        setIsConnect(false);
      });

      client.on("reconnect", () => {
        // setConnectStatus("Reconnecting");
        client.end();
        setIsConnect(null);
      });

      client.on("message", (topic, message) => {
        if (topic.includes("/resultcheckstatus")) {
          const payload = {
            topic,
            message: decodeJSONMessage(message.toString()),
          };
          console.log("🚀 ~ client.on ~ payload:", payload)
          setPayload(payload);
        }
      });
    }
  }, [client]);

  const mqttDisconnect = () => {
    if (client) {
      try {
        client.end(false, () => {
          setConnectStatus("Connect");
          setIsConnect(false);
          setPayload(null);
          console.log("disconnected successfully");
        });
      } catch (error) {
        console.log("disconnect error:", error);
      }
    }
  };

  const mqttPublish = (context: { topic: any; qos: any; payload: any }) => {
    if (client) {
      const { topic, qos, payload } = context;
      client.publish(topic, payload, { qos }, (error) => {
        if (error) {
          console.log("Publish error: ", error);
        }
      });
    }
  };

  const mqttSub = (subscription: { topic: any; qos: any }) => {
    if (client) {
      const { topic, qos } = subscription;

      client.subscribe(topic, { qos }, (error) => {
        if (error) {
          console.log("Subscribe to topics error", error);
          return;
        }
        
        if (topic.includes("/checkstatus")) {
          setIsPublishCheckSub(true);
          setSubscriptionDetail(topic);
        } else if (topic.includes("/resultcheckstatus")) {
          console.log("sub topic .../resultcheckstatus");
        }
      });
    }
  };

  const mqttUnSub = (subscription: { topic: any; qos: any }) => {
    if (client) {
      const { topic, qos } = subscription;
      client.unsubscribe(topic, { qos } as any, (error) => {
        if (error) {
          console.log("Unsubscribe error", error);
          return;
        }
        console.log(`unsubscribed topic: ${topic}`);

        setPayload(null);
        setIsPublishCheckSub(false);
      });
    }
  };

  const handleTriggerChange = (trigger: boolean) => {
    setMerchantChanged(trigger);
    setPayload(null)
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="grid gap-4 grid-cols-12 w-full">
        <div className="col-span-12">
          <CheckStatusConnection
            connect={mqttConnect}
            disconnect={mqttDisconnect}
            connectBtn={connectStatus}
          />
        </div>
        <QosOption.Provider value={qosOption as any}>
          <div className="col-span-12 lg:col-span-6">
            <PublishCheck
              sub={mqttSub}
              unSub={mqttUnSub}
              publish={mqttPublish}
              isConnected={isConnect}
              triggerMerchantChange={handleTriggerChange}
            />
          </div>
          <div className="col-span-12 lg:col-span-6">
            <ResultStatus
              sub={mqttSub}
              unSub={mqttUnSub}
              isPublishCheckSubscribe={isPublishCheckSubed}
              publishCheckSubDetail={subscriptionDetail}
              isConnected={isConnect}
              payload={payload}
              isMerchantChanged={isMerchantChanged}
            />
          </div>
        </QosOption.Provider>
      </div>
    </div>
  );
}
