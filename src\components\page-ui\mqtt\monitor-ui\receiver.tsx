import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/shadcn-ui/card";
import { Input } from "@/components/shadcn-ui/input";
import StatusBadge from "@/components/ui/status-badge";
import TooltipHover from "@/components/ui/tooltip-hover";
import { createData } from "@/lib/api-service";
import { cn, convertDate, JSONMessage } from "@/lib/utils";
import { ChevronDown, RefreshCcw, RefreshCw, Search } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

type Props = {
  payload: ClientMessage;
  dbTopics?: DbTopic[];
  loading: boolean;
  triggerRefreshData: () => Promise<void>;
};

interface ClientMessage {
  topic: string;
  message: string;
  createdAt: string;
  isSync: boolean;
}

interface DbMessage {
  id: number;
  content: string;
  isSync: boolean;
  createdAt: string;
  topicId: number;
}

interface DbTopic {
  id: number;
  name: string;
  messages: DbMessage[];
  isSync: boolean;
  createdAt: string;
}

interface ExpandedTopicsState {
  [key: number]: boolean;
}

export default function MonitorReceiver({
  payload,
  dbTopics,
  loading,
  triggerRefreshData,
}: Props | any) {
  const [messages, setMessages] = useState<Record<string, ClientMessage[]>>({});
  const messageQueue = useRef<any[]>([]);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  const mergedTopics =
    dbTopics?.topics?.map((topic: any) => {
      const mqttMessages =
        messages[topic.name]?.map((msg) => ({
          id: Math.floor(Math.random() * 100000),
          content: msg.message,
          isSync: msg.isSync,
          createdAt: new Date(),
          topicId: topic.id,
        })) || [];
      return {
        ...topic,
        messages: [...topic.messages, ...mqttMessages],
      };
    }) || [];

  const [expandedTopics, setExpandedTopics] = useState<ExpandedTopicsState>({});

  const handleSearchChange = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setSearchQuery(e.target.value);
  };

  useEffect(() => {
    const initialState: ExpandedTopicsState = {};
    dbTopics?.topics?.forEach((topic: any) => {
      initialState[topic.id] = true;
    });
    setExpandedTopics(initialState);
  }, [dbTopics]);

  const toggleExpand = (topicId: number) => {
    setExpandedTopics((prev) => ({
      ...prev,
      [topicId]: !prev[topicId],
    }));
  };

  useEffect(() => {
    if (payload.topic) {
      setMessages((prevMessages) => {
        const newMessages = { ...prevMessages };
        if (!newMessages[payload.topic]) {
          newMessages[payload.topic] = [];
        }
        newMessages[payload.topic].push({
          ...payload,
          createdAt: new Date(),
          isSync: false,
        });
        return newMessages;
      });
      setMessageInQueue(payload);
    }
  }, [payload]);

  const setMessageInQueue = (item: ClientMessage) => {
    messageQueue.current.push(item);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (messageQueue.current.length > 0) {
        createNewMessageInDB(messageQueue.current as any);
        messageQueue.current = [];
      }
    }, 10000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  };

  const createNewMessageInDB = async (message: ClientMessage) => {
    const params = {
      payload: message,
      hostUrl: dbTopics.host.url,
    };

    try {
      const response = await createData("topic", params);

      if (response.status === 200) {
        await triggerRefreshData();
        setMessages({});
      } else {
      }
    } catch (error) {
      console.error("Error creating message in DB:", error);
    }
  };

  const renderNoMessage = () => {
    if (Object.keys(messages).length === 0 && dbTopics.topics?.length === 0) {
      return (
        <div className="bg-secondary dark:bg-background border rounded-md p-4 flex flex-col gap-4 w-full">
          <div className="flex items-center justify-between">
            <span className="font-medium underline italic text-primary-foreground/50">
              topic
            </span>
          </div>
          <pre className="whitespace-normal italic text-primary-foreground/50 text-sm">
            message...
          </pre>
        </div>
      );
    }
    return null;
  };

  const filteredTopics = mergedTopics.filter(
    (topic: { name: string; messages: any[] }) => {
      const topicNameMatch = topic.name
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const messageContentMatch = topic.messages.some((msg) => {
        if (typeof msg.content === "object") {
          const jsonString = JSON.stringify(msg.content, null, 2);
          return jsonString.toLowerCase().includes(searchQuery.toLowerCase());
        } else if (typeof msg.content === "string") {
          return msg.content.toLowerCase().includes(searchQuery.toLowerCase());
        }
        return false;
      });

      return topicNameMatch || messageContentMatch;
    }
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center gap-2">
          <div className="flex justify-start items-center gap-1">
            <CardTitle className="text-xl md:text-2xl">Receiver</CardTitle>
            <TooltipHover content={<p>Update & Refresh</p>}>
              <button
                className="p-1 bg-transparent rounded-lg text-center scale-100 hover:scale-105 active:scale-100"
                onClick={() =>
                  createNewMessageInDB(messageQueue.current as any)
                }
              >
                <RefreshCw
                  className={
                    (cn("text-zinc-500 dark:text-white/70 transition"),
                    loading && "animate-spin")
                  }
                />
              </button>
            </TooltipHover>
          </div>
          <div className="relative ml-auto flex-1 grow-0">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="w-32 rounded-md bg-background pl-8 sm:w-44 lg:w-64"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="w-full mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-4">
            {renderNoMessage()}
            {Object.keys(messages)
              .reverse()
              .filter(
                (topic) =>
                  !mergedTopics.some(
                    (dbTopic: DbTopic) => dbTopic.name === topic
                  )
              )
              .map((topic, i) => (
                <div
                  key={i}
                  className={cn(
                    "custom-scrollbar bg-secondary h-80 md:h-[400px] overflow-y-scroll dark:bg-background transition-colors border rounded-md p-4 gap-4 flex flex-col"
                    // topic.isSync ? "border-green-600" : "border-zinc-700"
                  )}
                >
                  <div className="flex items-center justify-between">
                    <TooltipHover content={<p>{topic}</p>}>
                      <h5 className="font-medium underline text-sm sm:text-base md:text-lg truncate cursor-default">
                        {topic}
                      </h5>
                    </TooltipHover>
                  </div>
                  {messages[topic]
                    .slice()
                    .reverse()
                    .map((item, j) => (
                      <div
                        key={j}
                        className="bg-white/80 dark:bg-muted/90 p-2 rounded-md flex flex-col gap-2"
                      >
                        <div className="flex items-center justify-center gap-2 pb-2 overflow-x-scroll custom-scrollbar">
                          <pre className="text-sm w-full">
                            {JSONMessage(item.message)}
                          </pre>
                        </div>
                        <div className="flex flex-col">
                          <div className="flex items-center justify-between gap-2">
                            {item.isSync ? (
                              <StatusBadge type="success" title="sync" />
                            ) : (
                              <StatusBadge type="secondary" title="no-sync" />
                            )}
                            <span className="text-xs text-muted-foreground">
                              {item.createdAt.toLocaleString()}
                            </span>
                          </div>
                          <hr className="bg-zinc-700 dark:bg-zinc-100 h-0.5 my-2" />
                        </div>
                      </div>
                    ))}
                </div>
              ))}
            {filteredTopics.reverse().map((topic: DbTopic) => (
              <div
                key={topic.id}
                className={cn(
                  "custom-scrollbar bg-secondary overflow-y-scroll dark:bg-background transition-colors border rounded-md p-4 gap-4 flex flex-col",
                  topic.isSync ? "border-green-600" : "border-zinc-700",
                  expandedTopics[topic.id]
                    ? "h-80 md:h-[400px] transition"
                    : "h-fit transition"
                )}
              >
                <div className="flex items-center justify-between gap-2">
                  <TooltipHover content={<p>{topic.name}</p>}>
                    <h5 className="font-medium underline text-xs min-[400px]:text-sm max sm:text-base md:text-lg truncate cursor-default">
                      {topic.name}
                    </h5>
                  </TooltipHover>
                  <button
                    type="button"
                    className="p-1 rounded-lg bg-transparent text-center scale-100 hover:scale-105 active:scale-100"
                    onClick={() => toggleExpand(topic.id)}
                  >
                    <ChevronDown
                      className={cn(
                        "md:h-7 md:w-7 h-5 w-5",
                        expandedTopics[topic.id]
                          ? "rotate-180 transition"
                          : "rotate-0 transition"
                      )}
                    />
                  </button>
                </div>
                {expandedTopics[topic.id] && (
                  <>
                    {topic.messages
                      ?.slice()
                      .sort((a: DbMessage, b: DbMessage) => b.id - a.id)
                      .map((msg: DbMessage) => (
                        <div
                          key={msg.id}
                          className="bg-white/80 dark:bg-muted/90 p-2 rounded-md flex flex-col gap-2"
                        >
                          <div className="flex items-center justify-center gap-2 pb-2 overflow-x-scroll custom-scrollbar">
                            <pre className="text-sm w-full">
                              {JSONMessage(msg.content)}
                            </pre>
                          </div>
                          {/* <hr className="bg-zinc-700 dark:bg-zinc-100 h-0.5" /> */}
                          <div className="flex flex-col">
                            <div className="flex items-center justify-between gap-2">
                              {msg.isSync ? (
                                <StatusBadge type="success" title="sync" />
                              ) : (
                                <StatusBadge type="secondary" title="no-sync" />
                              )}
                              <span className="text-xs text-muted-foreground">
                                {convertDate(msg.createdAt)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
