"use client";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { endPontList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  clearCacheOrderStatusSchema,
  IClearCacheOrderStatusSchema,
} from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useEffect, useState } from "react";
import { getData } from "@/lib/api-service";
import Combobox from "@/components/ui/combobox";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CircleX, Lo<PERSON>2 } from "lucide-react";
import { Input } from "@/components/shadcn-ui/input";
import { Session } from "next-auth";
import AlertModal from "@/components/ui/alert-modal";
import axios from "axios";
import { cn, endPointToken } from "@/lib/utils";
import { admigLog } from "@/lib/actions";
import CardActionStatus from "@/components/ui/card-action-status";

type Props = {
  session: Session;
  configs: any;
};

export default function ClearCacheOrderStatus({ session, configs }: Props) {
  const isFixProject = configs.isFixProject;

  const {
    control,
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<IClearCacheOrderStatusSchema>({
    resolver: zodResolver(clearCacheOrderStatusSchema),
    defaultValues: {
      endPoint: isFixProject ? session.user.endPoint : "",
    },
  });

  const [loading, setLoading] = useState(false);
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [isClear, setIsClear] = useState<boolean | null>(null);
  const [merchant, setMerchant] = useState<any>();
  const [merchantLoading, setMerchantLoading] = useState<boolean>(false);
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const [isSelectedKT, setIsSelectedKT] = useState<boolean>(false);
  const clearAlert = () => setShowAlert(null);

  const renderSelectProject = () => {
    switch (isFixProject) {
      case true:
        return (
          <Controller
            name="endPoint"
            control={control}
            render={({ field }) => {
              const selectedItem = endPontList.find(
                (item) => item.url === field.value
              );
              return (
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    setValue("endPoint", value);
                    setValue("merchantId", "");
                    setValue("orderId", "");
                    setMerchant(null);
                    const selectedEndPoint = endPontList.find(
                      (endpoint) => endpoint.url === value
                    );
                    if (selectedEndPoint?.name === "API_KT") {
                      setIsSelectedKT(true);
                    } else {
                      setIsSelectedKT(false);
                      findMerchant(value, "merchant/filter");
                    }
                  }}
                  value={field.value}
                  disabled={isFixProject}
                >
                  <SelectTrigger id="project" aria-label="Select project">
                    <SelectValue placeholder="Select project">
                      {selectedItem ? selectedItem.name : "Select Project"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {endPontList.map((item) => (
                      <SelectItem key={item.id} value={item.url}>
                        {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            }}
          />
        );

      case false:
        return (
          <Select
            onValueChange={(value) => {
              setValue("endPoint", value);
              const selectedEndPoint = endPontList.find(
                (endpoint) => endpoint.url === value
              );
              if (selectedEndPoint?.name === "API_KT") {
                setIsSelectedKT(true);
              } else {
                setIsSelectedKT(false);
                findMerchant(value, "merchant/filter");
              }
              setMerchant(null);
              setValue("merchantId", ""), setValue("orderId", "");
            }}
          >
            <SelectTrigger id="project" aria-label="Select project">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {endPontList.map((item) => (
                <SelectItem key={item.id} value={item.url}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
    }
  };

  const findMerchant = async (endPoint: string, apiPath: string) => {
    setMerchantLoading(true);
    setMerchant(null);
    try {
      // const response = await getData(endPoint, apiPath);
      const response = await axios.get(`${endPoint}/api/${apiPath}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(endPoint as string),
        },
      });
      if (response.status === 200) {
        setMerchant(response.data);
        setMerchantLoading(false);
      } else {
        setMerchantLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
    } catch {
      setMerchantLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, can not find merchant id",
        onClose: clearAlert,
      });
    }
  };

  useEffect(() => {
    if (isFixProject) {
      const endPoint = getValues("endPoint");
      if (endPoint) {
        findMerchant(endPoint, "merchant/filter");
      }
    }
  }, [isFixProject, getValues]);

  const validateFormData = (data: IClearCacheOrderStatusSchema): boolean => {
    let isValid = true;

    if (!data.endPoint) {
      setError("endPoint", {
        type: "server",
        message: "Please select Project.",
      });
      isValid = false;
    }

    if (!data.merchantId) {
      setError("merchantId", {
        type: "server",
        message: "Please select Merchant ID.",
      });
      isValid = false;
    }

    if (!data.orderId) {
      setError("orderId", {
        type: "server",
        message: "Please enter Order ID.",
      });
      isValid = false;
    }

    return isValid;
  };

  const openAlert = (data: IClearCacheOrderStatusSchema) => {
    if (!validateFormData(data)) {
      return;
    }
    setIsAlertOpen(true);
  };

  const handleCloseAlert = () => {
    setIsAlertOpen(false);
  };

  const handleConfirm = async () => {
    setLoading(true);
    setIsClear(null);
    let url = `clearcache/orderstatus/${getValues("merchantId")}/${getValues(
      "orderId"
    )}`;

    try {
      // const response = await getData(getValues("endPoint"), url);
      const response = await axios.get(`${getValues("endPoint")}/api/${url}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(getValues("endPoint") as string),
        },
      });

      if (response.status === 200) {
        setIsClear(true);
        setLoading(false);
      } else {
        setIsClear(false);
        setLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        `${getValues("endPoint")}`,
        session?.user?.name || "Anonymous",
        "GET",
        `${getValues("endPoint")}/api/${url}`,
        "",
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setIsClear(false);
      setLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="flex flex-col gap-4 justify-center items-center w-full relative">
        <div className="w-full lg:w-[calc(100%-250px)] xl:w-[calc(100%-500px)] transition-all">
          <form onSubmit={handleSubmit(openAlert)}>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl md:text-2xl">
                  Clear Cache Order Status
                </CardTitle>
                <CardDescription>
                  {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6">
                  <div className="grid gap-3">
                    <Label htmlFor="project">Project</Label>
                    {renderSelectProject()}
                    {errors.endPoint && (
                      <p className="text-sm text-red-500">
                        {errors.endPoint.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="merchantId">Merchant ID</Label>
                    {isSelectedKT ? (
                      <Input
                        {...register("merchantId")}
                        id="merchantId"
                        name="merchantId"
                        type="text"
                        placeholder="Enter Merchant ID"
                        onChange={() => setIsClear(null)}
                      />
                    ) : (
                      <Combobox
                        title="Merchant ID"
                        listData={merchant}
                        valueKey="merchantId"
                        nameKey="merchantName"
                        slugKey="slug"
                        loading={merchantLoading}
                        showValueWithName={true}
                        onValueChange={(selectedValue) => {
                          setValue("merchantId", selectedValue);
                          setValue("orderId", ""), setIsClear(null);
                        }}
                      />
                    )}
                    {merchant && errors.merchantId && (
                      <p className="text-sm text-red-500">
                        {errors.merchantId.message}
                      </p>
                    )}
                  </div>
                  <div className="grid gap-3">
                    <Label htmlFor="orderId">Order ID</Label>
                    <Input
                      {...register("orderId")}
                      id="orderId"
                      name="orderId"
                      type="text"
                      placeholder="Enter Order ID"
                      onChange={() => setIsClear(null)}
                    />
                    {errors.orderId && (
                      <p className="text-sm text-red-500">
                        {errors.orderId.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="gap-2 justify-center">
                <Button
                  type="submit"
                  disabled={isSubmitting || loading}
                  className="w-full"
                >
                  <span className="flex items-center justify-center gap-2">
                    {loading ? (
                      <>
                        <p>Clearing...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Clear</p>
                      </>
                    )}
                  </span>
                </Button>
              </CardFooter>
            </Card>
            <AlertModal
              title="Clear Cache Confirmation"
              desc="This action will remove all cached data from our servers and cannot be undone."
              type="warning"
              open={isAlertOpen}
              isVerify={false}
              onClose={handleCloseAlert}
              onSubmit={handleConfirm}
            />
          </form>
        </div>
        <CardActionStatus
          loading={loading}
          isSuccess={isClear}
          successMessage="Clear Cache Success"
          errorMessage="Clear Cache failed"
          loadingMessage="Clearing..."
        />
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
