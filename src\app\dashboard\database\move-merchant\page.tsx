import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from "react";
import SettingConfig from "@/components/page-ui/pos-config/setting-config";
import { databaseList } from "@/lib/data";

export const metadata: Metadata = {
  title: "Move Merchant",
};

export default async function POSConfigPage() {
  const session = await getServerSession(authOptions);
  const configs = await getConfig();

  return (
    <>
      {/* <TitleHeader title="POS Config" type="fixed" /> */}
      <SettingConfig configs={configs} session={session as Session} />
    </>
  );
}
