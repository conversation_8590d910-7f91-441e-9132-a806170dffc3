import { IMqttSubscriberSchema, mqttSubscriberSchema } from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useContext } from "react";
import { Controller, useForm } from "react-hook-form";
import { QosOption } from "./playground";
import { Button } from "@/components/shadcn-ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import { Input } from "@/components/shadcn-ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { Loader2 } from "lucide-react";

type SubscriberProps = {
  sub: any;
  unSub: any;
  isSubscribe: boolean;
};

const record = {
  topic: "infogrammer-test",
  qos: "0",
};

export default function Subscriber({
  sub,
  unSub,
  isSubscribe,
}: SubscriberProps) {
  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
    watch,
    control,
  } = useForm<IMqttSubscriberSchema>({
    resolver: zodResolver(mqttSubscriberSchema),
    defaultValues: record,
  });

  const values = watch();
  const qosOptions = useContext(QosOption);

  const onQosChange = (value: string) => {
    setValue("qos", value);
  };

  const validateFormData = (data: IMqttSubscriberSchema): boolean => {
    let isValid = true;

    if (!data.topic) {
      setError("topic", {
        type: "server",
        message: "Please enter topic.",
      });
      isValid = false;
    }

    if (!data.qos) {
      setError("qos", {
        type: "server",
        message: "Please select QoS.",
      });
      isValid = false;
    }

    return isValid;
  };

  const onSubmit = async (data: IMqttSubscriberSchema) => {
    if (!validateFormData(data)) {
      return;
    }

    try {
      const { topic, qos } = data;
      const value = {
        topic,
        qos: parseInt(qos),
      };
      sub(value);
    } catch (error) {
      console.log("🚀 ~ onSubmit ~ error:", error);
    }
  };

  const handleUnsub = () => {
    unSub(values);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl md:text-2xl">Subscriber</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="grid gap-3">
              <Label htmlFor="topic">Topic</Label>
              <Input
                {...register("topic")}
                id="topic"
                name="topic"
                type="text"
                placeholder=""
              />
              {errors.topic && (
                <p className="text-sm text-red-500">{errors.topic.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="qos">QoS</Label>
              <Controller
                name="qos"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      onQosChange(value);
                    }}
                    value={field.value}
                  >
                    <SelectTrigger id="qos" name="qos" aria-label="Select qos">
                      <SelectValue>{field.value || "Select QoS"}</SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {qosOptions.map((item: any) => (
                        <SelectItem key={item.value} value={item.value}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.qos && (
                <p className="text-sm text-red-500">{errors.qos.message}</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="gap-2 justify-center">
          {/* <Button type="reset" variant={"outline"}>
                  Reset
                </Button> */}
          <Button
            type="submit"
            className={`${
              isSubscribe ? "bg-green-600 hover:bg-green-700" : " bg-primary"
            } w-full`}
            disabled={isSubmitting}
          >
            <span className="flex items-center justify-center gap-2">
              {isSubmitting ? (
                <>
                  <p>Subscribing...</p>
                  <Loader2 className="h-4 w-4 animate-spin" />
                </>
              ) : (
                <>
                  <p>{`${isSubscribe ? "Subscribed" : "Subscribe"}`}</p>
                </>
              )}
            </span>
          </Button>
          {isSubscribe && (
            <Button
              type="button"
              className="w-full"
              variant={"secondary"}
              onClick={handleUnsub}
            >
              Unsubscribe
            </Button>
          )}
        </CardFooter>
      </Card>
    </form>
  );
}
