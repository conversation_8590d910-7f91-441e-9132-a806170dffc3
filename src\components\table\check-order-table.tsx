import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shadcn-ui/table";
import { Card, CardContent } from "@/components/shadcn-ui/card";
import { TableRowError, TableRowIdle, TableRowSkeleton } from "./table-row";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import { formatDate } from "@/lib/utils";

type Props = {
  isLoading: boolean;
  data: any;
};

export default function CheckOrderTable({ isLoading, data }: Props) {
  const parseTableName = (tableName: any) => {
    if (typeof tableName === "object" && tableName !== null) {
      return tableName;
    }

    if (typeof tableName === "string") {
      try {
        const jsonLike = tableName
          .replace(/'/g, '"')
          .replace(/(\w+):/g, '"$1":');

        return JSON.parse(jsonLike);
      } catch (e) {
        console.error("Invalid tableName format:", tableName);
      }
    }

    return {};
  };

  const parsedTable = parseTableName(data?.tableName);

  return (
    <>
      <Card className="bg-sidebar">
        <CardContent>
          <ScrollArea className="mt-6 border rounded-md">
            <Table>
              <TableHeader className="bg-primary">
                <TableRow>
                  <TableHead className="text-primary-foreground font-bold text-base md:text-lg rounded-tl-md w-[35%]">
                    Table
                  </TableHead>
                  <TableHead className="text-primary-foreground font-bold text-base md:text-lg rounded-tr-md w-[65%] text-center">
                    Data
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.merchant === "Merchant is null" ? (
                  <TableRowError colSpan={2} title="No data avaliable!" />
                ) : data ? (
                  <>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">ZoneID</div>
                      </TableCell>
                      <TableCell className="text-center">
                        {parsedTable.zoneid?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">TableID</div>
                      </TableCell>
                      <TableCell className="text-center">
                        {parsedTable.tableid?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">TableName</div>
                      </TableCell>
                      <TableCell className="text-center">
                        {parsedTable.tablename?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">BuffetID</div>
                      </TableCell>
                      <TableCell className="text-center">
                        {parsedTable.buffetid?.toString() || " "}
                      </TableCell>
                    </TableRow>
                  </>
                ) : isLoading ? (
                  <TableRowSkeleton numCol={2} />
                ) : (
                  <TableRowIdle title="Search to see data." colSpan={2} />
                )}
              </TableBody>
            </Table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </CardContent>
      </Card>
      <Card className="bg-sidebar">
        <CardContent>
          <ScrollArea className="mt-6 border rounded-md">
            <Table>
              <TableHeader className="bg-primary">
                <TableRow>
                  <TableHead className="text-primary-foreground font-bold text-base md:text-lg rounded-tl-md w-[35%]">
                    Order
                  </TableHead>
                  <TableHead className="text-primary-foreground font-bold text-base md:text-lg rounded-tr-md lg:text-center w-[65%]">
                    Data
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.merchant === "Merchant is null" ? (
                  <TableRowError colSpan={2} title="No data avaliable!" />
                ) : data ? (
                  <>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">OrderID</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.orderid?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">Slug</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.slug?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">MerchantID</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.merchantId?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">OrderNO</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.orderNo?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">CustomData</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.customData?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">DeliveryJobID</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.deliveryJobId?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">DeliveryAgentID</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.deliveryAgentId?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">OrderStatus</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.orderStatus?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">PreviousOrderStatus</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.previousOrderStatus?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">NumOfGuest</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.numOfGuest?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">DeliverID</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.driverId?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">StartTime</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.startTime?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">EndTime</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.endTime?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">InsertDate</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {formatDate(data?.order?.insertDate?.toString()) || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">Remark</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.remark?.toString() || " "}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>
                        <div className="font-medium">RedirectToOrderId</div>
                      </TableCell>
                      <TableCell className="lg:text-center">
                        {data?.order?.redirectToOrderId?.toString() || " "}
                      </TableCell>
                    </TableRow>
                  </>
                ) : isLoading ? (
                  <TableRowSkeleton numCol={2} />
                ) : (
                  <TableRowIdle title="Search to see data." colSpan={2} />
                )}
              </TableBody>
            </Table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </CardContent>
      </Card>
    </>
  );
}
