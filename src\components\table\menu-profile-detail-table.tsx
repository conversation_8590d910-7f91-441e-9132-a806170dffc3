"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/shadcn-ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/shadcn-ui/card";
import { Skeleton } from "@/components/shadcn-ui/skeleton";
import { ChevronDown } from "lucide-react";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/shadcn-ui/dialog";
import { TableRowIdle, TableRowSkeleton } from "./table-row";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";

type Props = {
  isLoading: boolean;
  data: any;
};

type openSubMenuProps = {
  [key: number]: boolean;
};

export default function MenuProfileDetailTable({ isLoading, data }: Props) {
  const [expandedCategories, setExpandedCategories] =
    useState<openSubMenuProps>({});

  const handleToggle = (index: number) => {
    setExpandedCategories((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }));
  };

  const handleImageError = (e: any) => {
    e.currentTarget.src = "/assets/img-not-avaliable.jpg";
    e.currentTarget.onerror = null;
  };

  return (
    <Card>
      <CardContent>
        <ScrollArea className="mt-6 border whitespace-nowrap rounded-md">
          <Table>
            <TableHeader className="bg-primary">
              <TableRow>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center rounded-tl-md w-[10%]">
                  No.
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[22%] truncate">
                  Menu Name
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[20%] truncate">
                  Menu Code
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-xs md:text-sm text-center w-[23%] truncate">
                  Menu Category Code
                </TableHead>
                <TableHead className="text-primary-foreground font-bold text-sm md:text-base text-center w-[15%]">
                  Preview
                </TableHead>
                <TableHead className="rounded-tr-md w-[5%]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data ? (
                <>
                  {data.menuCategories.map((item: any, i: number) => (
                    <React.Fragment key={i}>
                      <TableRow>
                        <TableCell className="font-medium text-center">
                          {item?.sort || " "}
                        </TableCell>
                        <TableCell className="text-center">
                          {item?.menuCategoryName || " "}
                        </TableCell>
                        <TableCell className="text-center">
                          {item?.menuCategoryCode || " "}
                        </TableCell>
                        <TableCell className="text-center">
                          {item?.menuCategoryId || " "}
                        </TableCell>
                        <TableCell className="text-center">
                          <Dialog>
                            <DialogTrigger className="cursor-pointer hover:underline">
                              Detail
                            </DialogTrigger>
                            <DialogContent className="max-h-[95%] rounded-lg">
                              <DialogHeader>
                                <DialogTitle>
                                  {item?.menuCategoryName}
                                </DialogTitle>
                                <span className="py-2">
                                  <hr className="bg-primary" />
                                </span>
                                <div className="flex justify-center items-center flex-col gap-3">
                                  <picture>
                                    <img
                                      src={
                                        item?.imagePath
                                          ? `${
                                              process.env.STORAGE_URL
                                            }/resize?id=${encodeURIComponent(
                                              item?.imagePath
                                            )}`
                                          : "/assets/img-not-avaliable.jpg"
                                      }
                                      alt={item?.menuCategoryName}
                                      className="object-cover w-32 h-32 object-center bg-secondary rounded-md border-2 border-primary"
                                      onError={handleImageError}
                                    />
                                  </picture>
                                  <ScrollArea className="h-[400px] w-full rounded-md">
                                    <Table className="border">
                                      <TableBody>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MerchantID
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.merchantId?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MenuID
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.menuId?.toString() || " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MenuCode
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.menuCategoryCode?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MenuCategoryCode
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.menuCategoryId?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MenuProfileID
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.menuProfileId?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            ItemCategoryID
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.itemCategoryId?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MenuCategoryDescription
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.menuCategoryDescription?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            MenuProfileName
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.menuProfileName?.toString() ||
                                                " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            Sort
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.sort?.toString() || " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell className="font-medium text-left">
                                            PageID
                                          </TableCell>
                                          <TableCell className="text-center">
                                            <DialogDescription>
                                              {item?.pageId?.toString() || " "}
                                            </DialogDescription>
                                          </TableCell>
                                        </TableRow>
                                      </TableBody>
                                    </Table>
                                  </ScrollArea>
                                </div>
                              </DialogHeader>
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                        <TableCell>
                          {item.menus?.length !== 0 && (
                            <button
                              type="button"
                              className={`${
                                item.menus?.length !== 0
                                  ? "p-0.5 text-center rounded-full bg-primary cursor-pointer scale-100 hover:scale-105 active:scale-100"
                                  : "p-0.5 text-center rounded-full bg-secondary cursor-default hover:scale-100"
                              }`}
                              onClick={() => handleToggle(i)}
                              disabled={item.menus?.length === 0}
                            >
                              <ChevronDown className="h-5 w-5 text-primary-foreground text-center" />
                            </button>
                          )}
                        </TableCell>
                      </TableRow>
                      {expandedCategories[i] &&
                        item.menus &&
                        item.menus?.map((subItem: any, j: number) => (
                          <TableRow key={j} className=" bg-secondary">
                            <TableCell className="text-center">
                              {subItem.sort || " "}
                            </TableCell>
                            <TableCell className="text-center">
                              {subItem.menuName || " "}
                            </TableCell>
                            <TableCell className="text-center">
                              {subItem.menuCode || " "}
                            </TableCell>
                            <TableCell className="text-center"></TableCell>
                            <TableCell className="text-center">
                              <Dialog>
                                <DialogTrigger className="cursor-pointer hover:underline">
                                  Detail
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>
                                      {subItem.menuName}
                                    </DialogTitle>
                                    <span className="py-2">
                                      <hr className="bg-primary" />
                                    </span>
                                    <DialogDescription className="flex justify-center items-center flex-col gap-3">
                                      <picture>
                                        <img
                                          src={
                                            subItem.imagePath
                                              ? `${
                                                  process.env.STORAGE_URL
                                                }/resize?id=${encodeURIComponent(
                                                  subItem.imagePath
                                                )}`
                                              : "/assets/img-not-avaliable.jpg"
                                          }
                                          alt={subItem.menuName}
                                          className="object-cover w-32 h-32 object-center bg-secondary rounded-md border-2 border-primary"
                                          onError={handleImageError}
                                        />
                                      </picture>
                                      <span className="text-base font-semibold">
                                        MenuCode : {subItem.menuCode}
                                      </span>
                                    </DialogDescription>
                                  </DialogHeader>
                                </DialogContent>
                              </Dialog>
                            </TableCell>
                            <TableCell></TableCell>
                          </TableRow>
                        ))}
                    </React.Fragment>
                  ))}
                </>
              ) : isLoading ? (
                <TableRowSkeleton numCol={6} />
              ) : (
                <TableRowIdle title="Search to see data." colSpan={6} />
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
