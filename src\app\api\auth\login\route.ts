import axios from "axios";
import { NextResponse } from "next/server";
import xml2js, { parseStringPromise } from "xml2js";

export async function POST(request: Request) {
  const { username, password } = await request.json();
  const soapRequest = createSoapRequest(username, password);

  try {
    const response = await axios.post(
      "http://infosvr.thaiddns.com/license/RegistrationService.asmx",
      soapRequest,
      {
        headers: {
          "Content-Type": "text/xml; charset=utf-8",
          SOAPAction: "http://restaurantprogram.net/UserAuthorize",
        },
      }
    );

    const responseText = response.data;

    const parser = new xml2js.Parser({ explicitArray: false });
    const result = await parser.parseStringPromise(responseText);

    const userAuthorizeResult =
      result["soap:Envelope"]["soap:Body"]["UserAuthorizeResponse"][
        "UserAuthorizeResult"
      ];

    return NextResponse.json({
      username,
      password,
      isCorrect: userAuthorizeResult === "true",
    });
  } catch (error) {
    console.error("Error during SOAP request:", error);
    return NextResponse.json({ username, password, isCorrect: false });
  }
}

const createSoapRequest = (username: string, password: string): string => {
  return `<?xml version="1.0" encoding="utf-8"?>
    <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
      <soap12:Body>
        <UserAuthorize xmlns="http://restaurantprogram.net/">
          <sUserName>${username}</sUserName>
          <sPassword>${password}</sPassword>
        </UserAuthorize>
      </soap12:Body>
    </soap12:Envelope>`;
};
