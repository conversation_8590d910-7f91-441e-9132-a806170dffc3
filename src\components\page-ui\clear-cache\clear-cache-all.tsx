"use client";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>it<PERSON>,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/shadcn-ui/select";
import { endPontList } from "@/lib/data";
import { Button } from "@/components/shadcn-ui/button";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { clearCacheAllSchema, IClearCacheAllSchema } from "@/lib/types";
import { AlertBar, AlertProps } from "@/components/ui/alert-bar";
import { useState } from "react";
import { getData } from "@/lib/api-service";
import { CircleCheckBig, CircleX, Loader2 } from "lucide-react";
import AlertModal from "@/components/ui/alert-modal";
import { Session } from "next-auth";
import axios from "axios";
import { cn, endPointToken } from "@/lib/utils";
import { admigLog } from "@/lib/actions";
import CardActionStatus from "@/components/ui/card-action-status";

type Props = {
  session: Session;
  configs: any;
};

export default function ClearCacheAll({ session, configs }: Props) {
  const isFixProject = configs.isFixProject;

  const {
    control,
    register,
    getValues,
    setValue,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<IClearCacheAllSchema>({
    resolver: zodResolver(clearCacheAllSchema),
    defaultValues: {
      endPoint: isFixProject ? session.user.endPoint : "",
    },
  });

  const [loading, setLoading] = useState(false);
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [isClear, setIsClear] = useState<boolean | null>(null);
  const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
  const clearAlert = () => setShowAlert(null);

  const renderSelectProject = () => {
    switch (isFixProject) {
      case true:
        return (
          <Controller
            name="endPoint"
            control={control}
            render={({ field }) => {
              const selectedItem = endPontList.find(
                (item) => item.url === field.value
              );
              return (
                <Select
                  onValueChange={(value) => {
                    field.onChange(value), setValue("endPoint", value);
                  }}
                  value={field.value}
                  disabled={isFixProject}
                >
                  <SelectTrigger id="project" aria-label="Select project">
                    <SelectValue placeholder="Select project">
                      {selectedItem ? selectedItem.name : "Select Project"}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {endPontList.map((item) => (
                      <SelectItem key={item.id} value={item.url}>
                        {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              );
            }}
          />
        );

      case false:
        return (
          <Select
            onValueChange={(value) => {
              setValue("endPoint", value);
            }}
          >
            <SelectTrigger id="project" aria-label="Select project">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {endPontList.map((item) => (
                <SelectItem key={item.id} value={item.url}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
    }
  };

  const validateFormData = (data: IClearCacheAllSchema): boolean => {
    let isValid = true;

    if (!data.endPoint) {
      setError("endPoint", {
        type: "server",
        message: "Please select Project.",
      });
      isValid = false;
    }

    return isValid;
  };

  const openAlert = (data: IClearCacheAllSchema) => {
    if (!validateFormData(data)) {
      return;
    }
    setIsAlertOpen(true);
  };

  const handleCloseAlert = () => {
    setIsAlertOpen(false);
  };

  const handleConfirm = async () => {
    setLoading(true);
    setIsClear(null);
    let url = `clearcache`;

    try {
      // const response = await getData(getValues("endPoint"), url);
      const response = await axios.get(`${getValues("endPoint")}/api/${url}`, {
        headers: {
          Accept: "application/json",
          key: endPointToken(getValues("endPoint") as string),
        },
      });
      if (response.status === 200) {
        setIsClear(true);
        setLoading(false);
      } else {
        setIsClear(false);
        setLoading(false);
        setShowAlert({
          type: "warning",
          detail: "Search failed, please try again.",
          onClose: clearAlert,
        });
        return;
      }
      await admigLog(
        `${getValues("endPoint")}`,
        session?.user?.name || "Anonymous",
        "GET",
        `${getValues("endPoint")}/api/${url}`,
        "",
        JSON.stringify(response.data),
        response.status.toString()
      );
    } catch {
      setIsClear(false);
      setLoading(false);
      setShowAlert({
        type: "error",
        detail: "Something went wrong, please try again later",
        onClose: clearAlert,
      });
    }
  };
  
  // Function ClearCache ทุกสาขาของ Teenoi
  // const handleBatchClear = async (selectedMerchantIds: number[]) => {
  //   const merchantIds = [
  //     1010, 618, 1011, 971, 1233, 1032, 988, 1230, 1229, 893, 831, 986, 685,
  //     970, 959, 1005, 1080, 866, 969, 1136, 1225, 1058, 975, 972, 1227, 785,
  //     1134, 983, 1232, 742, 682, 626, 992, 867, 840, 1007, 1019, 999, 633, 1218,
  //     716, 1015, 764, 1152, 1041, 830, 1228, 784, 1106, 1064, 984, 1013, 1226,
  //     1050, 1224, 863, 1153, 1219, 767, 1124, 960, 1042, 961, 1199, 1002, 1086,
  //     1105, 1003, 666, 1114, 991, 1016, 839, 1008, 976, 926, 810, 1205, 1004,
  //     766, 357, 937, 1021,
  //   ]; // รายชื่อ Merchant IDs
  //   setLoading(true);
  //   setIsClear(null);

  //   const endPoint = getValues("endPoint");

  //   for (const merchantId of merchantIds) {
  //     const url = `clearcache/merchant/${merchantId}`;
  //     try {
  //       const response = await axios.get(`${endPoint}/api/${url}`, {
  //         headers: {
  //           Accept: "application/json",
  //           key: endPointToken(endPoint as string),
  //         },
  //       });

  //       if (response.status === 200) {
  //         console.log(
  //           `Successfully cleared cache for Merchant ID: ${merchantId}`
  //         );
  //         setShowAlert({
  //           type: "success",
  //           detail: `Successfully cleared cache for Merchant ID: ${merchantId}`,
  //           onClose: clearAlert,
  //         });
  //       } else {
  //         console.error(`Failed to clear cache for Merchant ID: ${merchantId}`);
  //         setShowAlert({
  //           type: "warning",
  //           detail: `Failed to clear cache for Merchant ID: ${merchantId}`,
  //           onClose: clearAlert,
  //         });
  //       }
  //     } catch (error) {
  //       console.error(
  //         `Error clearing cache for Merchant ID: ${merchantId}`,
  //         error
  //       );
  //       setShowAlert({
  //         type: "error",
  //         detail: `Error clearing cache for Merchant ID: ${merchantId}`,
  //         onClose: clearAlert,
  //       });
  //     }
  //   }

  //   setLoading(false);
  //   setShowAlert({
  //     type: "success",
  //     detail: "Batch clear cache process completed.",
  //     onClose: clearAlert,
  //   });
  // };

  return (
    <div className="flex flex-1 items-start justify-start">
      <div className="flex flex-col gap-4 justify-center items-center w-full relative">
        <div className="w-full lg:w-[calc(100%-250px)] xl:w-[calc(100%-500px)] transition-all">
          <form onSubmit={handleSubmit(openAlert)}>
            <Card>
              <CardHeader>
                <CardTitle className="text-xl md:text-2xl">
                  Clear Cache All
                </CardTitle>
                <CardDescription>
                  {/* Lipsum dolor sit amet, consectetur adipiscing elit */}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6">
                  <div className="grid gap-3">
                    <Label htmlFor="project">Project</Label>
                    {renderSelectProject()}
                    {errors.endPoint && (
                      <p className="text-sm text-red-500">
                        {errors.endPoint.message}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="gap-2 justify-center">
                <Button
                  type="submit"
                  disabled={isSubmitting || loading}
                  className="w-full"
                >
                  <span className="flex items-center justify-center gap-2">
                    {loading ? (
                      <>
                        <p>Clearing...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Clear</p>
                      </>
                    )}
                  </span>
                </Button>
                {/* ปุ่ม for loop clercache MerchantId ของ Teenoi
                [
                  1010, 618, 1011, 971, 1233, 1032, 988, 1230, 1229, 893, 831, 986, 685, 970, 
                  959, 1005, 1080, 866, 969, 1136, 1225, 1058, 975, 972, 1227, 785, 1134, 983, 
                  1232, 742, 682, 626, 992, 867, 840, 1007, 1019, 999, 633, 1218, 716, 1015, 
                  764, 1152, 1041, 830, 1228, 784, 1106, 1064, 984, 1013, 1226, 1050, 1224, 863, 
                  1153, 1219, 767, 1124, 960, 1042, 961, 1199, 1002, 1086, 1105, 1003, 666, 1114, 
                  991, 1016, 839, 1008, 976, 926, 810, 1205, 1004, 766, 357, 937, 1021
                ]
                */}
                {/* <Button
                  type="button"
                  disabled={isSubmitting || loading}
                  onClick={() => {
                    const selectedMerchantIds = [357, 1224, 1225, 1226, 1227, 1228, 1229, 1230, 1232, 1233]; // ใส่ Merchant IDs ที่ต้องการ
                    handleBatchClear(selectedMerchantIds);
                  }}
                  className="w-full"
                >
                  <span className="flex items-center justify-center gap-2">
                    {loading ? (
                      <>
                        <p>Clearing Batch...</p>
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        <p>Clear Multiple</p>
                      </>
                    )}
                  </span>
                </Button> */}
              </CardFooter>
            </Card>
            <AlertModal
              title="Clear Cache Confirmation"
              desc="This action will remove all cached data from our servers and cannot be undone."
              type="warning"
              open={isAlertOpen}
              isVerify={true}
              onClose={handleCloseAlert}
              onSubmit={handleConfirm}
            />
          </form>
        </div>
        <CardActionStatus
          loading={loading}
          isSuccess={isClear}
          successMessage="Clear Cache Success"
          errorMessage="Clear Cache failed"
          loadingMessage="Clearing..."
        />
      </div>
      {showAlert && (
        <AlertBar
          type={showAlert.type}
          detail={showAlert.detail}
          onClose={clearAlert}
        />
      )}
    </div>
  );
}
